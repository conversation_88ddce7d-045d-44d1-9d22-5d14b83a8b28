{'姓名': '测试正竹昭', '手机号': '19819121346', '身份证号': '341204200003092425', '企业名称': 'esigntest正竹昭经营的个体工商户', '社会编码': '91000000W22FYU8D25', '银行卡号': '621494450616814532'}
{'姓名': '测试卫磊', '手机号': '19867489525', '身份证号': '340207200203127096', '企业名称': 'esigntest卫磊经营的个体工商户', '社会编码': '910000007PNCX4QB1X', '银行卡号': '620709385164695441'}
{'姓名': '测试孟霞', '手机号': '19887450742', '身份证号': '340122197603101542', '企业名称': 'esigntest孟霞经营的个体工商户', '社会编码': '91000000AWRBXUY66W', '银行卡号': '6244291281711521'}
{'姓名': '测试郜雪露', '手机号': '19833438009', '身份证号': '350203199203237544', '企业名称': 'esigntest郜雪露经营的个体工商户', '社会编码': '91000000N96H5RKA78', '银行卡号': '6217641090093011'}
{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试丘梁成', '手机号': '***********', '身份证号': '654224196909286236', '企业名称': 'esigntest丘梁成经营的个体工商户', '社会编码': '91000000D2Q25KTG95', '银行卡号': '6292603960316373284'}
用户信息{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:1测试丘梁成{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试丘梁成', 'mobile': '198****4688', 'realnameMobile': '198****4688', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '65**************36', 'code': {'CRED_PSN_CH_IDCARD': '65**************36'}, 'contactMobile': '198****4688', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '852b086e319a4f9792dd1518c5383204', 'guid': 'b3ec6de5de7a43ecb4066ca3198b27d4', 'ouid': '1e3616ffb80d4942905674cab9589fba', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '654224196909286236'}, 'serviceId': '3897440918831637084', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
用户信息:2esigntest丘梁成经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest丘梁成经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************95', 'code': {'CRED_ORG_USCC': '91**************95'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': '6b5eb35f5dcc428abcf8f0ace5fc55ec', 'guid': '77f9f2ddd604447985792b66e0d5a3fe', 'organGuid': '77f9f2ddd604447985792b66e0d5a3fe', 'ouid': 'f5c4f33e5378490ca8013240985272be', 'adminInfoList': [{'mobile': '198****4688', 'email': None, 'name': '测试丘梁成', 'ouid': '1e3616ffb80d4942905674cab9589fba', 'accountUid': '852b086e319a4f9792dd1518c5383204', 'guid': 'b3ec6de5de7a43ecb4066ca3198b27d4', 'credentialNo': '65**************36', 'credentialType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserType': None, 'thirdpartyUserId': None}], 'roleAmount': 1, 'organId': 'c94468425813496da344a1a715e1ed62', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试丘梁成', 'orgLegalNo': '65**************36', 'orgLegalType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '91000000D2Q25KTG95'}, 'serviceId': '3898445436671236534', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '91000000D2Q25KTG95'}], 'total': 1}}
{'姓名': '测试正昌', '手机号': '***********', '身份证号': '530823199505052993', '企业名称': 'esigntest正昌经营的个体工商户', '社会编码': '91000000BC04LD5P4W', '银行卡号': '6216707386325325657'}
用户信息:2esigntest正昌经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试牛国', '手机号': '19804937628', '身份证号': '520400195303028817', '企业名称': 'esigntest牛国经营的个体工商户', '社会编码': '91000000BGT59HYC69', '银行卡号': '3568576537343946'}
用户信息:2esigntest牛国经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest牛国经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试蔚筠茗', '手机号': '19811371931', '身份证号': '621222200212061844', '企业名称': 'esigntest蔚筠茗经营的个体工商户', '社会编码': '91000000527CH97XXY', '银行卡号': '621387973560790879'}
{'姓名': '测试籍良', '手机号': '19810268312', '身份证号': '42068419911218799X', '企业名称': 'esigntest籍良经营的个体工商户', '社会编码': '91000000U1N7FRC61Q', '银行卡号': '6213560765284017509'}
{'姓名': '测试吴善', '手机号': '19815525646', '身份证号': '451222200401111132', '企业名称': 'esigntest吴善经营的个体工商户', '社会编码': '91000000HGER14N566', '银行卡号': '6224338920630572'}
{'姓名': '测试封榕', '手机号': '***********', '身份证号': '532527194904118193', '企业名称': 'esigntest封榕经营的个体工商户', '社会编码': '91000000U1FKD2GW8R', '银行卡号': '6222950010158041'}
用户信息:2esigntest封榕经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试融勤', '手机号': '***********', '身份证号': '542422198112176160', '企业名称': 'esigntest融勤经营的个体工商户', '社会编码': '91000000MPYRPN5Q15', '银行卡号': '****************'}
用户信息:1测试融勤{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试融勤', 'mobile': '198****2220', 'realnameMobile': '198****2220', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '54**************60', 'code': {'CRED_PSN_CH_IDCARD': '54**************60'}, 'contactMobile': '198****2220', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': 'e2c703da253f4b1f84958096fe238a8f', 'guid': 'ad1957c21fbf48faa2b4cc081b8e19ba', 'ouid': '18b0a68f7745404298ed86e10426986d', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '542422198112176160'}, 'serviceId': '3901811987780743502', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
{'姓名': '测试强亮亮', '手机号': '***********', '身份证号': '******************', '企业名称': 'esigntest强亮亮经营的个体工商户', '社会编码': '910000009YNFQD3L3D', '银行卡号': '****************'}
用户信息:2esigntest强亮亮经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest强亮亮经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************3D', 'code': {'CRED_ORG_USCC': '91**************3D'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': '6daa6ea1fb8b4665a6c53df73b156d29', 'guid': 'e007099f0c69435a951bcb6083eea809', 'organGuid': 'e007099f0c69435a951bcb6083eea809', 'ouid': 'b5ecb22306a846d08a33f50b087c888e', 'adminInfoList': [], 'roleAmount': 0, 'organId': '4a91e03c1e794c9b9c256e8aa9cd9d07', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试强亮亮', 'orgLegalNo': '', 'orgLegalType': '', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '910000009YNFQD3L3D'}, 'serviceId': '3901822280401432852', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '910000009YNFQD3L3D'}], 'total': 1}}
{'姓名': '测试封榕', '手机号': '***********', '身份证号': '532527194904118193', '企业名称': 'esigntest封榕经营的个体工商户', '社会编码': '91000000U1FKD2GW8R', '银行卡号': '6222950010158041'}
{'姓名': '测试孔颖佳', '手机号': '19839387418', '身份证号': '************113308', '企业名称': 'esigntest孔颖佳经营的个体工商户', '社会编码': '91000000F3D2295N1G', '银行卡号': '6205526932703451'}
用户信息:1测试孔颖佳{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest孔颖佳经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试符锦', '手机号': '19827621314', '身份证号': '330783196405211061', '企业名称': 'esigntest符锦经营的个体工商户', '社会编码': '91000000PGGE6D9C6F', '银行卡号': '6250280142023839'}
用户信息:1测试符锦{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试习永伟', '手机号': '***********', '身份证号': '341824197611079498', '企业名称': 'esigntest习永伟经营的个体工商户', '社会编码': '91000000AB4FGA2NXB', '银行卡号': '6229768811052098440'}
用户信息:1测试习永伟{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试习永伟', 'mobile': '198****7175', 'realnameMobile': '198****7175', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '34**************98', 'code': {'CRED_PSN_CH_IDCARD': '34**************98'}, 'contactMobile': '198****7175', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '4a3accd195a8436a9bd494c5a87b978c', 'guid': '6097f32c52024968a065d04f8b0663d4', 'ouid': '6966a62026f54c2b81d10e7b20672dff', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '341824197611079498'}, 'serviceId': '3902822335518355791', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
{'姓名': '测试劳家', '手机号': '***********', '身份证号': '140424199808034313', '企业名称': 'esigntest劳家经营的个体工商户', '社会编码': '910000001BN95HG62X', '银行卡号': '6213560515448626863'}
{'姓名': '测试连时鹏', '手机号': '***********', '身份证号': '654002195702163737', '企业名称': 'esigntest连时鹏经营的个体工商户', '社会编码': '91000000493XFGLH6E', '银行卡号': '6236521740823985465'}
用户信息:1测试连时鹏{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:1测试连时鹏{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试连时鹏', 'mobile': '198****8597', 'realnameMobile': '198****8597', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '65**************37', 'code': {'CRED_PSN_CH_IDCARD': '65**************37'}, 'contactMobile': '198****8597', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': 'd841b1d64966480d9f56141099ba73bc', 'guid': '2672ba3435d04242bf2a938e96ffc04c', 'ouid': '9c703eeca93040e3b6de88571d41f694', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '654002195702163737'}, 'serviceId': '3907695449754568848', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
{'姓名': '测试红英', '手机号': '***********', '身份证号': '532531199610087384', '企业名称': 'esigntest红英经营的个体工商户', '社会编码': '91000000U5XWHYEQ3A', '银行卡号': '****************'}
用户信息:1测试红英{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest红英经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试温波', '手机号': '19809508043', '身份证号': '******************', '企业名称': 'esigntest温波经营的个体工商户', '社会编码': '910000007R1QKBYU66', '银行卡号': '****************'}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
{'姓名': '测试空云', '手机号': '***********', '身份证号': '130523195007312001', '企业名称': 'esigntest空云经营的个体工商户', '社会编码': '91000000BR0PF9P82M', '银行卡号': '6230854992591214129'}
用户信息:2esigntest温波经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [], 'total': 0}}
用户信息:1测试空云{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试空云', 'mobile': '198****0611', 'realnameMobile': '198****0611', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '13**************01', 'code': {'CRED_PSN_CH_IDCARD': '13**************01'}, 'contactMobile': '198****0611', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '130523195007312001'}, 'serviceId': '3910411377340582958', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
用户信息:1测试空云{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试空云', 'mobile': '198****0611', 'realnameMobile': '198****0611', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '13**************01', 'code': {'CRED_PSN_CH_IDCARD': '13**************01'}, 'contactMobile': '198****0611', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '130523195007312001'}, 'serviceId': '3910411377340582958', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
用户信息:1测试空云{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试空云', 'mobile': '198****0611', 'realnameMobile': '198****0611', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '13**************01', 'code': {'CRED_PSN_CH_IDCARD': '13**************01'}, 'contactMobile': '198****0611', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '130523195007312001'}, 'serviceId': '3910411377340582958', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
用户信息:1测试空云{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': '测试空云', 'mobile': '198****0611', 'realnameMobile': '198****0611', 'bankCardNum': None, 'email': None, 'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '13**************01', 'code': {'CRED_PSN_CH_IDCARD': '13**************01'}, 'contactMobile': '198****0611', 'contactEmail': None, 'realNameStatus': 'ACCEPT', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'thirdPartyIdcards': None, 'createTime': *************, 'modifyTime': *************, 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'uuid': None, 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'freezeStatus': 1, 'freezeReason': None, 'lockStatus': 1, 'gidInfo': {'codeType': 'CRED_PSN_CH_IDCARD', 'codeValue': '130523195007312001'}, 'serviceId': '3910411377340582958', 'createSource': None, 'registerSource': 'SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB'}], 'total': 1}}
用户信息:2esigntest空云经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest空云经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************2M', 'code': {'CRED_ORG_USCC': '91**************2M'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': 'cab54f85f8e7489aa2802512246fae05', 'guid': 'fbc3f04bc12148859e5286339858b344', 'organGuid': 'fbc3f04bc12148859e5286339858b344', 'ouid': '561ad5a27a09407ca260ee2cfc2c0a48', 'adminInfoList': [{'mobile': '198****0611', 'email': None, 'name': '测试空云', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'credentialNo': '13**************01', 'credentialType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserType': None, 'thirdpartyUserId': None}], 'roleAmount': 1, 'organId': '1d67ca9145d347039b6e0997ec1eef64', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试空云', 'orgLegalNo': '', 'orgLegalType': '', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '91000000BR0PF9P82M'}, 'serviceId': '3910448609170558049', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '91000000BR0PF9P82M'}], 'total': 1}}
{'姓名': '测试简韵蓉', '手机号': '***********', '身份证号': '652700196906092203', '企业名称': 'esigntest简韵蓉经营的个体工商户', '社会编码': '91000000E2C17PD600', '银行卡号': '6213820100932951923'}
用户信息:2esigntest空云经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest空云经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************2M', 'code': {'CRED_ORG_USCC': '91**************2M'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': 'cab54f85f8e7489aa2802512246fae05', 'guid': 'fbc3f04bc12148859e5286339858b344', 'organGuid': 'fbc3f04bc12148859e5286339858b344', 'ouid': '561ad5a27a09407ca260ee2cfc2c0a48', 'adminInfoList': [{'mobile': '198****0611', 'email': None, 'name': '测试空云', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'credentialNo': '13**************01', 'credentialType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserType': None, 'thirdpartyUserId': None}], 'roleAmount': 1, 'organId': '1d67ca9145d347039b6e0997ec1eef64', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试空云', 'orgLegalNo': '', 'orgLegalType': '', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '91000000BR0PF9P82M'}, 'serviceId': '3910448609170558049', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '91000000BR0PF9P82M'}], 'total': 1}}
{'姓名': '测试闻荣', '手机号': '***********', '身份证号': '520103200112145566', '企业名称': 'esigntest闻荣经营的个体工商户', '社会编码': '91000000PPJL98W95J', '银行卡号': '6216270076984081801'}
用户信息:2esigntest空云经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest空云经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************2M', 'code': {'CRED_ORG_USCC': '91**************2M'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': 'cab54f85f8e7489aa2802512246fae05', 'guid': 'fbc3f04bc12148859e5286339858b344', 'organGuid': 'fbc3f04bc12148859e5286339858b344', 'ouid': '561ad5a27a09407ca260ee2cfc2c0a48', 'adminInfoList': [{'mobile': '198****0611', 'email': None, 'name': '测试空云', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'credentialNo': '13**************01', 'credentialType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserType': None, 'thirdpartyUserId': None}], 'roleAmount': 1, 'organId': '1d67ca9145d347039b6e0997ec1eef64', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试空云', 'orgLegalNo': '', 'orgLegalType': '', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '91000000BR0PF9P82M'}, 'serviceId': '3910448609170558049', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '91000000BR0PF9P82M'}], 'total': 1}}
{'姓名': '测试雍娜', '手机号': '***********', '身份证号': '130803199701250305', '企业名称': 'esigntest雍娜经营的个体工商户', '社会编码': '91000000M8QW3NCH0B', '银行卡号': '6213460693184681367'}
用户信息:2esigntest空云经营的个体工商户{'errCode': 0, 'message': '成功', 'data': {'items': [{'name': 'esigntest空云经营的个体工商户', 'codeType': 'CRED_ORG_USCC', 'codeValue': '91**************2M', 'code': {'CRED_ORG_USCC': '91**************2M'}, 'realNameStatus': 'ACCEPT', 'createTime': *************, 'modifyTime': *************, 'accountUid': 'cab54f85f8e7489aa2802512246fae05', 'guid': 'fbc3f04bc12148859e5286339858b344', 'organGuid': 'fbc3f04bc12148859e5286339858b344', 'ouid': '561ad5a27a09407ca260ee2cfc2c0a48', 'adminInfoList': [{'mobile': '198****0611', 'email': None, 'name': '测试空云', 'ouid': 'b13923903f5943b8b6cbf8ff84f7f8d4', 'accountUid': '7c0b05fe07674d8981af492b3987d925', 'guid': '4c904d20e763406aa6d8c17a775801c2', 'credentialNo': '13**************01', 'credentialType': 'CRED_PSN_CH_IDCARD', 'thirdpartyUserType': None, 'thirdpartyUserId': None}], 'roleAmount': 1, 'organId': '1d67ca9145d347039b6e0997ec1eef64', 'deleted': False, 'productId': '**********', 'productName': 'PRODUCT_STD', 'orgLegalName': '测试空云', 'orgLegalNo': '', 'orgLegalType': '', 'thirdpartyUserId': None, 'thirdpartyUserType': None, 'gidInfo': {'codeType': 'CRED_ORG_USCC', 'codeValue': '91000000BR0PF9P82M'}, 'serviceId': '3910448609170558049', 'createSource': '**********', 'registerSource': 'SOURCE_APP_ID:**********', 'activate': 1, 'realNameOrganTransferRecord': None, 'realNameOrganTransferSource': None, 'addOrganChecklist': 0, 'normalCodeValue': '91000000BR0PF9P82M'}], 'total': 1}}
{'姓名': '测试贾武', '手机号': '***********', '身份证号': '36030219540327377X', '企业名称': 'esigntest贾武经营的个体工商户', '社会编码': '91000000YC11CTPLXK', '银行卡号': '621106088199431744'}
{'姓名': '甘舰戈', '手机号': '18458110367', '身份证号': '******************', '企业名称': '杭州书契网络科技合伙企业（有限合伙）', '社会编码': '91330108MA2KGB2B9B', '银行卡号': '6232727583532015096'}
