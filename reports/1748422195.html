<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-05-28 16:49:55</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">1.278 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (0/1)</td>
      <td colspan="2">18 (15/3/0/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>创建有账号证书申请任务</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 18</td>
      <td>SUCCESS: 15</td>
      <td>FAILED: 3</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-bizType枚举值_TIANYIN</td>
      <td style="text-align:center;width:6em;">71.68 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-bizType枚举值_TIANYIN</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TIANYIN&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:55 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221957274667
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 29
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221957274667
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/jhawW0W&#39;, &#39;taskId&#39;: &#39;8d7ddec3503a440f96663c0871699962&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=8d7ddec3503a440f96663c0871699962&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>71.68</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>65.716</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-bizType非法值</td>
      <td style="text-align:center;width:6em;">63.98 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-bizType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;INVALID_ENUM&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:55 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221958024669
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221958024669
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>124</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>63.98</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>60.866</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_APPLY</td>
      <td style="text-align:center;width:6em;">66.47 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_APPLY</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:55 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221958724671
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 28
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221958724671
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/Ux2rR65&#39;, &#39;taskId&#39;: &#39;1317610d15ec47c0ac507b57a34cf7d1&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=1317610d15ec47c0ac507b57a34cf7d1&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>66.47</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>63.299</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_UPDATE</td>
      <td style="text-align:center;width:6em;">62.40 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_UPDATE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;UPDATE&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:55 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221959444673
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221959444673
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;证书id不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>证书id不能为空</td>
                      <td>证书id不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>61</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>62.4</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>59.792</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_DELAY</td>
      <td style="text-align:center;width:6em;">65.48 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_DELAY</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;DELAY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221960144675
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 27
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221960144675
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;证书id不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>证书id不能为空</td>
                      <td>证书id不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>61</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>65.48</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>62.912</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_6">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-operatorType非法值</td>
      <td style="text-align:center;width:6em;">66.60 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_6_1">log-1</a>
        <div id="popup_log_1_6_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_6_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-operatorType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 465
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;INVALID_ENUM&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221960894677
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221960894677
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>122</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>66.6</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>63.955</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_7">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-userType枚举值_1</td>
      <td style="text-align:center;width:6em;">59.13 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_7_1">log-1</a>
        <div id="popup_log_1_7_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_7_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-userType枚举值_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221961554679
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221961554679
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;用户类型和传入的用户信息不一致&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>用户类型和传入的用户信息不一致</td>
                      <td>用户类型和传入的用户信息不一致</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>86</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>59.13</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>55.831</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_8">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-userType枚举值_2</td>
      <td style="text-align:center;width:6em;">67.87 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_8_1">log-1</a>
        <div id="popup_log_1_8_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_8_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-userType枚举值_2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221962244681
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 28
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221962244681
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/ffGZW3P&#39;, &#39;taskId&#39;: &#39;1f8dac59305142248d94c177bba618a3&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=1f8dac59305142248d94c177bba618a3&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>67.87</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>64.938</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_9">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-userType非法值</td>
      <td style="text-align:center;width:6em;">54.45 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_9_1">log-1</a>
        <div id="popup_log_1_9_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_9_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-userType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 3}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221962894683
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221962894683
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;用户类型和传入的用户信息不一致&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>用户类型和传入的用户信息不一致</td>
                      <td>用户类型和传入的用户信息不一致</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>86</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>54.45</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>52.07</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_10">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-agentType枚举范围_1</td>
      <td style="text-align:center;width:6em;">60.18 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_10_1">log-1</a>
        <div id="popup_log_1_10_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_10_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-agentType枚举范围_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 1}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221963524687
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 27
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221963524687
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/yShibIg&#39;, &#39;taskId&#39;: &#39;53189806f903422692d46ee3a7832250&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=53189806f903422692d46ee3a7832250&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>60.18</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>57.437</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_11">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-agentType枚举范围_2</td>
      <td style="text-align:center;width:6em;">56.88 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_11_1">log-1</a>
        <div id="popup_log_1_11_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_11_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-agentType枚举范围_2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 2}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221964154689
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221964154689
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/KokQaBq&#39;, &#39;taskId&#39;: &#39;0af24359ab3a4fb897e5735cacb23137&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=0af24359ab3a4fb897e5735cacb23137&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>56.88</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>54.526</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_12">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">反向用例-agentType非法值</td>
      <td style="text-align:center;width:6em;">70.27 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_12_1">log-1</a>
        <div id="popup_log_1_12_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_12_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-agentType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 3}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221964914691
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221964914691
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/FeUTIPa&#39;, &#39;taskId&#39;: &#39;ae4ed9dace454d2c8c8efefe0e417085&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=ae4ed9dace454d2c8c8efefe0e417085&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>代理人类型不在允许范围内</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>70.27</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>68.485</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_12">traceback</a>
          <div id="popup_attachment_1_12" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_12">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 代理人类型不在允许范围内(str)	==&gt; fail
成功(str) contains 代理人类型不在允许范围内(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 代理人类型不在允许范围内(str)	==&gt; fail
成功(str) contains 代理人类型不在允许范围内(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_13">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-algorithm枚举值_RSA</td>
      <td style="text-align:center;width:6em;">67.33 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_13_1">log-1</a>
        <div id="popup_log_1_13_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_13_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-algorithm枚举值_RSA</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221965674693
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221965674693
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/QKLpLWE&#39;, &#39;taskId&#39;: &#39;157e6d427fb44b5496b129c2d89b7d13&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=157e6d427fb44b5496b129c2d89b7d13&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>67.33</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>64.948</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_14">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-algorithm枚举值_SM2</td>
      <td style="text-align:center;width:6em;">57.64 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_14_1">log-1</a>
        <div id="popup_log_1_14_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_14_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-algorithm枚举值_SM2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;SM2&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221966334697
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 23
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221966334697
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/3jDtJy2&#39;, &#39;taskId&#39;: &#39;3f7ea2ad372d4e5a8e06010ee027bbd7&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=3f7ea2ad372d4e5a8e06010ee027bbd7&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>57.64</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>54.018</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_15">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">反向用例-algorithm非法值</td>
      <td style="text-align:center;width:6em;">65.43 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_15_1">log-1</a>
        <div id="popup_log_1_15_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_15_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-algorithm非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 473
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;INVALID_ENUM&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221966954699
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 32
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221966954699
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/plwgKed&#39;, &#39;taskId&#39;: &#39;67c7d3081d5f419a827f36180d18da2c&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=67c7d3081d5f419a827f36180d18da2c&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>算法类型不支持</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>65.43</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>62.773</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_15">traceback</a>
          <div id="popup_attachment_1_15" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_15">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 算法类型不支持(str)	==&gt; fail
成功(str) contains 算法类型不支持(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 算法类型不支持(str)	==&gt; fail
成功(str) contains 算法类型不支持(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_16">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-certType枚举值_SINGLE</td>
      <td style="text-align:center;width:6em;">58.93 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_16_1">log-1</a>
        <div id="popup_log_1_16_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_16_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-certType枚举值_SINGLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 466
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;SINGLE&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221967694709
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221967694709
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/LgJkBnm&#39;, &#39;taskId&#39;: &#39;d54bfe54e68d4b62ac9b286e529ed7c9&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=d54bfe54e68d4b62ac9b286e529ed7c9&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>58.93</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>56.143</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_17">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-certType枚举值_DOUBLE</td>
      <td style="text-align:center;width:6em;">61.79 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_17_1">log-1</a>
        <div id="popup_log_1_17_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_17_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-certType枚举值_DOUBLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 466
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;DOUBLE&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221968334711
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221968334711
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/K6WNDUw&#39;, &#39;taskId&#39;: &#39;1f277d704baa4de9950a479372049bcf&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=1f277d704baa4de9950a479372049bcf&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>61.79</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>59.691</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_18">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">反向用例-certType非法值</td>
      <td style="text-align:center;width:6em;">64.58 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_18_1">log-1</a>
        <div id="popup_log_1_18_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_18_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-certType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;INVALID_ENUM&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:49:56 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T285T17484221969034717
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 27
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T285T17484221969034717
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/uA35J3j&#39;, &#39;taskId&#39;: &#39;91458620e76b407aaaa8b459004677f9&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=91458620e76b407aaaa8b459004677f9&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>单双证类型不支持</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>64.58</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>62.169</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_18">traceback</a>
          <div id="popup_attachment_1_18" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_18">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 单双证类型不支持(str)	==&gt; fail
成功(str) contains 单双证类型不支持(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 1451001(int)	==&gt; fail
0(int) equals 1451001(int)

validate: content.message contains 单双证类型不支持(str)	==&gt; fail
成功(str) contains 单双证类型不支持(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
  </table>
  
</body>