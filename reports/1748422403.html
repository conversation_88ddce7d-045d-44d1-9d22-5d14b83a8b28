<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-05-28 16:53:23</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">1.586 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (1/0)</td>
      <td colspan="2">20 (20/0/0/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>创建有账号证书申请任务</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 20</td>
      <td>SUCCESS: 20</td>
      <td>FAILED: 0</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-bizType枚举值_TIANYIN</td>
      <td style="text-align:center;width:6em;">96.48 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-bizType枚举值_TIANYIN</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TIANYIN&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:23 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224037689035
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 55
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224037689035
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/7pWng4j&#39;, &#39;taskId&#39;: &#39;fe5184316cff4c6aa6223d478c890047&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=fe5184316cff4c6aa6223d478c890047&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>96.48</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>91.01</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-bizType非法值</td>
      <td style="text-align:center;width:6em;">67.71 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-bizType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;INVALID_ENUM&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:23 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224038739039
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224038739039
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>124</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>67.71</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>65.096</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_APPLY</td>
      <td style="text-align:center;width:6em;">68.12 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_APPLY</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:23 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224039489043
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224039489043
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/j6wbjmw&#39;, &#39;taskId&#39;: &#39;1b620e9e44de4130835f07b70e8ee2cf&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=1b620e9e44de4130835f07b70e8ee2cf&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>68.12</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>64.524</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_UPDATE</td>
      <td style="text-align:center;width:6em;">74.04 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_UPDATE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;UPDATE&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224040279045
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224040279045
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;证书id不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>证书id不能为空</td>
                      <td>证书id不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>61</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>74.04</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>70.402</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-operatorType枚举值_DELAY</td>
      <td style="text-align:center;width:6em;">61.46 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-operatorType枚举值_DELAY</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;DELAY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224040989051
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224040989051
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;证书id不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>证书id不能为空</td>
                      <td>证书id不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>61</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>61.46</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>58.517</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_6">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-operatorType非法值</td>
      <td style="text-align:center;width:6em;">73.84 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_6_1">log-1</a>
        <div id="popup_log_1_6_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_6_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-operatorType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 465
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;INVALID_ENUM&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224041789057
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 27
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224041789057
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM</td>
                      <td>No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>122</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>73.84</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>70.536</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_7">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-userType枚举值_1</td>
      <td style="text-align:center;width:6em;">62.57 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_7_1">log-1</a>
        <div id="popup_log_1_7_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_7_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-userType枚举值_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224042569065
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 28
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224042569065
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;用户类型和传入的用户信息不一致&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>用户类型和传入的用户信息不一致</td>
                      <td>用户类型和传入的用户信息不一致</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>86</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>62.57</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>59.584</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_8">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-userType枚举值_2</td>
      <td style="text-align:center;width:6em;">79.42 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_8_1">log-1</a>
        <div id="popup_log_1_8_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_8_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-userType枚举值_2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224043399067
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224043399067
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/RAHGJ8X&#39;, &#39;taskId&#39;: &#39;68896e3248584b5ab417c244b4218f7a&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=68896e3248584b5ab417c244b4218f7a&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>79.42</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>76.477</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_9">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-userType非法值</td>
      <td style="text-align:center;width:6em;">59.00 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_9_1">log-1</a>
        <div id="popup_log_1_9_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_9_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-userType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 458
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 3}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224044089079
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224044089079
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: &#39;用户类型和传入的用户信息不一致&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>1451001</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>用户类型和传入的用户信息不一致</td>
                      <td>用户类型和传入的用户信息不一致</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>86</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>59.0</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>56.206</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_10">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-agentType枚举范围_1</td>
      <td style="text-align:center;width:6em;">69.79 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_10_1">log-1</a>
        <div id="popup_log_1_10_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_10_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-agentType枚举范围_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 1}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224044819081
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224044819081
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/drhrslU&#39;, &#39;taskId&#39;: &#39;c2d852ab6e874cb8b14eb407c0406a4d&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=c2d852ab6e874cb8b14eb407c0406a4d&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>69.79</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>66.647</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_11">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-agentType枚举范围_2</td>
      <td style="text-align:center;width:6em;">63.63 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_11_1">log-1</a>
        <div id="popup_log_1_11_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_11_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-agentType枚举范围_2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 2}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224045559083
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224045559083
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/VOKFkaU&#39;, &#39;taskId&#39;: &#39;27c314a3ac9d40a691f2d7bf2058ae4a&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=27c314a3ac9d40a691f2d7bf2058ae4a&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>63.63</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>61.045</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_12">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-agentType非法值</td>
      <td style="text-align:center;width:6em;">86.22 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_12_1">log-1</a>
        <div id="popup_log_1_12_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_12_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-agentType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 3}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224046429089
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 31
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224046429089
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/05B0k8c&#39;, &#39;taskId&#39;: &#39;c819e0e4619b4263b2c4aad7e29aee17&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=c819e0e4619b4263b2c4aad7e29aee17&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>86.22</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>83.393</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_13">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-algorithm枚举值_RSA</td>
      <td style="text-align:center;width:6em;">55.19 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_13_1">log-1</a>
        <div id="popup_log_1_13_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_13_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-algorithm枚举值_RSA</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224047159097
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 24
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224047159097
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/6tuEkmz&#39;, &#39;taskId&#39;: &#39;0454667814c649b9a991fe72f209fe2f&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=0454667814c649b9a991fe72f209fe2f&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>55.19</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>51.838</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_14">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-algorithm枚举值_SM2</td>
      <td style="text-align:center;width:6em;">65.06 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_14_1">log-1</a>
        <div id="popup_log_1_14_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_14_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-algorithm枚举值_SM2</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 464
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;SM2&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224047809103
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224047809103
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/FRiYWJk&#39;, &#39;taskId&#39;: &#39;b7b7ba7ce78b47e3bea80f150028239b&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=b7b7ba7ce78b47e3bea80f150028239b&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>65.06</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>61.999</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_15">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-algorithm非法值</td>
      <td style="text-align:center;width:6em;">62.06 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_15_1">log-1</a>
        <div id="popup_log_1_15_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_15_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-algorithm非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 473
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;algorithm&#34;: &#34;INVALID_ENUM&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224048529105
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224048529105
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/wbTQrSn&#39;, &#39;taskId&#39;: &#39;e116e0f91b6e4037a858a59b19be717d&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=e116e0f91b6e4037a858a59b19be717d&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>62.06</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>60.023</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_16">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-agentType非法值</td>
      <td style="text-align:center;width:6em;">70.59 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_16_1">log-1</a>
        <div id="popup_log_1_16_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_16_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-agentType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1}, &#34;applyCommonModel&#34;: {&#34;agentType&#34;: 3}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:24 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224049239107
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 24
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224049239107
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/56UJFyq&#39;, &#39;taskId&#39;: &#39;fbe94a9b80d946b491d626d70f67932a&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=fbe94a9b80d946b491d626d70f67932a&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>70.59</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>66.809</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_17">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-certType非法值</td>
      <td style="text-align:center;width:6em;">88.55 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_17_1">log-1</a>
        <div id="popup_log_1_17_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_17_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-certType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;INVALID_ENUM&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:25 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224049999111
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224049999111
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/mOsrOV7&#39;, &#39;taskId&#39;: &#39;245bea11fc914a0eb4064a4895f2fa5d&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=245bea11fc914a0eb4064a4895f2fa5d&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>88.55</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>85.163</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_18">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-certType枚举值_SINGLE</td>
      <td style="text-align:center;width:6em;">78.30 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_18_1">log-1</a>
        <div id="popup_log_1_18_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_18_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-certType枚举值_SINGLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 466
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;SINGLE&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:25 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224050999117
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224050999117
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/9ymQcvI&#39;, &#39;taskId&#39;: &#39;c7bdfaddfbdc4bbbaae716cfd5d97dc9&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=c7bdfaddfbdc4bbbaae716cfd5d97dc9&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>78.3</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>74.826</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_19">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例-certType枚举值_DOUBLE</td>
      <td style="text-align:center;width:6em;">68.78 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_19_1">log-1</a>
        <div id="popup_log_1_19_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_19_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例-certType枚举值_DOUBLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 466
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;DOUBLE&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:25 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224051879123
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 24
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224051879123
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/CvSmmiW&#39;, &#39;taskId&#39;: &#39;b3315153f81e41519eb3886065dbb1ae&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=b3315153f81e41519eb3886065dbb1ae&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>68.78</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>66.037</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_20">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">反向用例-certType非法值</td>
      <td style="text-align:center;width:6em;">64.17 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_20_1">log-1</a>
        <div id="popup_log_1_20_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_20_1">&times;</a>

            <div class="content">
              <h3>Name: 反向用例-certType非法值</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722725
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 472
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: **********, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19888644846, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;INVALID_ENUM&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;esigntest\u82cd\u8bda\u7ecf\u8425\u7684\u4e2a\u4f53\u5de5\u5546\u6237&#34;, &#34;licenseNumber&#34;: &#34;91000000BL0X3BDKXJ&#34;, &#34;licenseType&#34;: 1, &#34;userType&#34;: 2}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 28 May 2025 08:53:25 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17484224052609129
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 23
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17484224052609129
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/CPLCLFt&#39;, &#39;taskId&#39;: &#39;2ab0dbe4d1bb45c08f4888b0fc8efe08&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=2ab0dbe4d1bb45c08f4888b0fc8efe08&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>64.17</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>60.622</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
  </table>
  
</body>