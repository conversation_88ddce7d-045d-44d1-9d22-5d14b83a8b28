import datetime
import random
import string
import time

import requests
import os
import json

class Config(object):
    # 使用人
    userName = "ganning"

    # 使用环境
    env = "test"  # proj, test, sml, prod
    appid = "7876722740" #e签宝证书 7876722740-带自定义域名  浙江ca 7876722915
    gid = "c83462f9f6314f06874bbfaaebf28d33"
    f_open_api = "in-test-openapi.tsign.cn"  #"footstone-open-api.testk8s.tsign.cn"  #footstone-open-api-esignca.projectk8s.tsign.cn

    svs_service = "svs-service.testk8s.tsign.cn"
    cert_service = "cert-service-esignca.projectk8s.tsign.cn"
    esignra = "esign-ra.testk8s.tsign.cn"
    运营支撑 = "testmanage.esign.cn"
    cookies = {
        "redirect_referer": "aHR0cHM6Ly90ZXN0c3VwcG9ydC50c2lnbi5jbi8=",
        "test_access_token": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "access_token": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }

class Config(object):
    # 使用人
    userName = "ganning"

    # 使用环境
    env = "sml"  # proj, test, sml, prod
    appid = "4439054963" #e签宝证书 4439054963-带自定义域名  浙江ca 4439054959
    f_open_api = "in-sml-openapi.tsign.cn"  #"footstone-open-api.testk8s.tsign.cn"  #footstone-open-api-esignca.projectk8s.tsign.cn
    svs_service = "svs-service.testk8s.tsign.cn"
    cert_service = "cert-service.smlk8s.esign.cn"
    esignra = "esign-ra.smlk8s.esign.cn"
    运营支撑 = "smlmanage.esign.cn"
    cookies = {
    "JSESSIONID": "828F9B145FFE4360B97FFB7B61F03101",
    "__bid_n": "19512ea794b745656fd331",
    "_arms_uid:.esign.cn": "uid_boghovhwnkdtzbk0",
    "Hm_lvt_6792858ba0a253fd0bafb4331786bea2": "1742796533,1742809407,1742869234,1742892551",
    "gf-gr-1020983": "1^#0.3",
    "redirect_referer": "aHR0cHM6Ly9zbWxzdXBwb3J0LmVzaWduLmNuLw==",
    "sml_access_token": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "test_access_token": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "arms_session:.esign.cn": "d6d4b152ad3e44798cde0a6e3570a821-1-1747652686760-1747653077696",
}


# 项目环境
x_group = os.environ.get('env_name')
# 本地自定义的项目环境
env = os.environ.get("env")
# 判断当前系统，如果是windows，就是用windows的项目环境
if os.name == 'nt':
    # Windows系统
    x_group = "eSignCA"


class TaskStatusException(Exception):
    """自定义任务状态异常"""
    pass


headers = {
    "Content-Type": "application/json"
}
headers["X-Tsign-Open-App-Id"] = Config.appid
#"X-Tsign-Open-Auth-Mode": "simple"
headers["X-Tsign-Open-Auth-Mode"] = "simple"
if Config.env == "proj":
    # "X-Tsign-Service-Group": x_group
    headers["X-Tsign-Service-Group"] = x_group
elif Config.env == "test":
    pass


def 请求接口(url, method, headers=headers, data=None):
    """
    发送HTTP请求并处理响应。

    :param url: 请求的URL
    :param method: 请求方法（GET, POST, PUT, DELETE等）
    :param headers: 请求头（可选）
    :param data: 请求体数据（可选）
    :return: 如果状态码为200，返回响应的JSON对象；否则返回None
    """
    try:
        # 动态获取 requests 模块中的方法
        request_method = getattr(requests, method.lower(), None)
        if request_method is None:
            print(f"Unsupported HTTP method: {method}")
            return None

        # 发送请求
        if method.upper() in ['POST', 'PUT']:
            response = request_method(url, headers=headers, json=data)
        else:
            response = request_method(url, headers=headers)

        if response.status_code == 200:
            #将整个请求记录在 请求记录.txt
            data = {
                "url": url,
                "method": method,
                "headers": headers,
                "入参": data,
                "响应":  response.text
            }
            with open("请求记录.txt", "a", encoding="utf-8") as f:
                f.write(json.dumps(data, ensure_ascii=False) + "\n")
            return response.json()
        else:
            print(f"Request failed with status code {response.status_code}: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"Request exception: {e}")
        return None

def 获取个人或企业的实名组织(个人或企业=1,名称="aaa"):
    headersUse = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Origin": "https://testsupport.tsign.cn",
        "Referer": "https://testsupport.tsign.cn/microfe/usercenter/list",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "cross-site",
        "Sec-Fetch-Storage-Access": "active",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "x-requested-with": "XMLHttpRequest"
    }


    if 个人或企业 == 1:
        url = f"https://{Config.运营支撑}/manage-account/account/personalAccount"
        """{"sorts":[{"sortType":"desc","sortKey":"data.base.createTime"}],"deleted":false,"pageIndex":0,"pageSize":20,"orgRealNameStatus":"","realNameStatus":"ACCEPT","activate":"","createTime":"","name":"测试葛飞"}
"""
        data = {
            "sorts": [{"sortType": "desc", "sortKey": "data.base.createTime"}],
            "deleted": False,
            "pageIndex": 0,
            "pageSize": 20,
            "orgRealNameStatus": "",
            "realNameStatus": "ACCEPT",
            "activate": "",
            "createTime": "",
            "name": 名称
        }


    elif 个人或企业 == 2:
        url = f"https://{Config.运营支撑}/manage-account/account/organAccount"
        data = {
            "sorts": [{"sortType": "desc", "sortKey": "createTime"}],
            "deleted": "",
            "pageIndex": 0,
            "pageSize": 20,
            "orgRealNameStatus": True,
            "realNameStatus": "ACCEPT",
            "activate": 1,
            "createTime": "",
            "name": 名称
        }

    response = requests.post(url, headers=headersUse, cookies=Config.cookies, json=data)

    print("实名组织Response Body:", response.json())
    with open("测试信息.txt", "a", encoding="utf8") as f:  # 使用 "a" 实现追加写入
        f.write(f"用户信息:{个人或企业}{名称}"+str(response.json()))
        f.write("\n")
    items = response.json().get("data").get("items")
    if len(items) >=1:
        return True
    else:
        return False

def 获取创建个人或企业的基本信息(使用老数据=1):
    if 使用老数据:
        with open("测试信息.txt", "r", encoding="utf8") as f:#读取 测试信息.txt的最新一条，然后返回
            readlines = f.readlines()[::-1]
            for i in readlines:
                if "用户信息" not in i :
                    readline = i
                    break
            accountDataNew = eval(readline)
            return accountDataNew


    # 定义请求参数
    url = "http://sdk.testk8s.tsign.cn/random/get"
    method = "POST"

    data = {
        "env": "模拟环境" if Config.env == "sml" else "测试环境",
        "mock": True,
        "total": 1
    }

    # 使用 请求接口 方法发送请求
    response = 请求接口(url, method, headers=headers, data=data)

    accountData = response["accountList"][0]
    accountDataNew = {}
    accountDataNew["姓名"] = accountData["name"]
    accountDataNew["手机号"] = accountData["phone"]
    accountDataNew["身份证号"] = accountData["idNo"]
    accountDataNew["企业名称"] = accountData["orgName"]
    accountDataNew["社会编码"] = accountData["orgCode"]
    accountDataNew["银行卡号"] = accountData["bankCard"]
    # accountDataNew["英文姓名"] = accountData["englishName"]

    print(f"实名所需要的参数信息{accountDataNew}")
    #写入到 测试信息.txt里  做增量写入
    with open("测试信息.txt", "a", encoding="utf8") as f:  # 使用 "a" 实现追加写入
        f.write(str(accountDataNew))
        f.write("\n")
    return accountDataNew


def 创建证书申请任务接口(个人or企业=1, oid=None, 产品=[1, 2, 3],使用老数据=1):
    """

    :param 个人or企业:用户类型 1-个人 2-企业
    :param 产品: 1、TIANYIN、TCLOUD 3、ESHIELD  业务方类型 (枚举: TIANYIN,TIANYIN_OFFLINE,ESHIELD,TCLOUD,PUBCLOUD)
    oid :如果传入oid，则不使用姓名和证件号
    :return:
    """

    for product in 产品:
        if product == 1:
            bizType = "TIANYIN"
        elif product == 2:
            bizType = "TCLOUD"
        elif product == 3:
            bizType = "ESHIELD"
        else:
            print("产品类型错误")
            return

        url = f"http://{Config.f_open_api}/v1/certs/create-cert-task"
        if oid:
            data = {
                "bizType": bizType,
                "operatorType": "APPLY",
                "projectId": Config.appid,  # appid
                "notifyUrl": "http://libaohui.com.cn/callback/ding",
                "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
                "applyConfigModel": {
                    "certNum": 1,
                    # "certType":"DOUBLE","issuer":"","algorithm":"SM2"
                },
                "applyCommonModel": {
                },
                "applyUserModel": {
                    "userType": 1 if 个人or企业 == 1 else 2

                },

            }
            data["applyUserModel"]["oid"] = oid
            print(f"\033[91m获取创建个人或企业使用oid{oid}\033[0m")
        else:
            accountDataNew = 获取创建个人或企业的基本信息(使用老数据=使用老数据)
            print(f"\033[91m获取创建个人或企业的基本信息{accountDataNew}\033[0m")
            # accountDataNew = {'姓名': '测试延锦瑾', '手机号': '***********', '身份证号': '610600194911170882', '企业名称': 'esigntest延锦瑾经营的个体工商户',
            #                   '社会编码': '91000000HDPFTJM27Y', '银行卡号': '****************'}

            data = {
                "bizType": bizType,
                "operatorType": "APPLY",
                "projectId": Config.appid,  # appid
                "notifyUrl": "http://libaohui.com.cn/callback/ding",
                "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
                "phone": accountDataNew["手机号"],
                "applyConfigModel": {
                    "certNum": 1,
                    "certType": "SINGLE", "issuer": "GUOXINCA", "algorithm": "RSA"

                },
                "applyCommonModel": {
                },
                "applyUserModel": {
                    "certName": accountDataNew["姓名"] if 个人or企业 == 1 else accountDataNew["企业名称"],
                    "licenseNumber": accountDataNew["身份证号"] if 个人or企业 == 1 else accountDataNew["社会编码"],
                    "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码
                    "userType": 1 if 个人or企业 == 1 else 2,
                },

            }

        print(f"\033[91m{bizType}创建证书申请任务接口,入参是{data}\033[0m")
        method = "POST"
        response = 请求接口(url, method, headers=headers, data=data)
        print(f"{bizType}创建证书申请任务接口返回信息,：{response}")
        taskId = response["data"]["taskId"]
        # 新的写入
        生成的taskid保存和校验(taskId, data, "写入")
    # 老的执行全量巡检
    生成的taskid保存和校验(taskId, data, "读取")

def random_str(slen=3):#生成3个随机汉字
    #生成3个随机汉字
    aa = "金飞达四六级哦福大搜热弯房间大附件打死哦年卡理发打撒富家大室颗粒剂覅哦啊哦瑞特钱啊我妈那"
    aa = [i for i in aa]
    return "".join(random.sample(aa, slen))

def 更新和延期证书申请任务接口(更新和延期=1, 个人or企业=1, certId=None, 延期时长="ONEYEAR", bizType="TIANYIN"):
    if 更新和延期 == 1:
        data = {
            "bizType": bizType,
            "operatorType": "UPDATE",
            "projectId": Config.appid,  # appid
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
            "applyCommonModel": {
                "agentType": 0
            },
            "applyConfigModel": {
                "algorithm": "RSA",
                "certTime": 延期时长,
                "certType": "SINGLE",
                "issuer": "ZHCA",
                "certNum": 1,
                "certId": certId
            },
            "applyUserModel": {
                "userType": 1 if 个人or企业 == 1 else 2,
                "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码
                "certName": "测试更新证书" + random_str() if  个人or企业 == 1 else "esigntest测试更新证书" + random_str(),  #拼接随机汉字

            }
        }
    elif 更新和延期 == 2:
        data = {
            "bizType": bizType,
            "operatorType": "DELAY",
            "projectId": Config.appid,  # appid
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
            "applyCommonModel": {
                "agentType": 0
            },
            "applyConfigModel": {
                "algorithm": "RSA",
                "certTime": "ONEYEAR",
                "certType": "SINGLE",
                "certNum": 1,
                "certId": certId
            },
            "applyUserModel": {
                "userType": 1 if 个人or企业 == 1 else 2,
                "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码

            }
        }
    url = f"http://{Config.f_open_api}/v1/certs/create-cert-task"
    print(f"\033[91m更新和延期证书申请任务接口,入参是{data}\033[0m")
    method = "POST"
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"更新和延期证书申请任务接口：{response}")
    taskId = response["data"]["taskId"]
    # 新的写入
    生成的taskid保存和校验(taskId, data, "写入")
    # 老的执行全量巡检
    生成的taskid保存和校验(taskId, data, "读取")


def 创建无账号证书申请任务接口(个人or企业=1, 产品=[1, 2, 3, 4],使用老数据=1):
    """

    :param 个人or企业:用户类型 1-个人 2-企业
    :param 产品: 1、TIANYIN_OFFLINE  2、PUBCLOUD 3、tcloud 4、ESHIELD 业务方类型 (枚举: TIANYIN,TIANYIN_OFFLINE,ESHIELD,TCLOUD,PUBCLOUD)
    oid:个人oid
    :return:
    """
    for product in 产品:
        if product == 1:
            bizType = "TIANYIN_OFFLINE"
        elif product == 2:
            bizType = "PUBCLOUD"
        elif product == 3:
            bizType = "TCLOUD"
        elif product == 4:
            bizType = "ESHIELD"
        else:
            print("产品类型错误")
            return

        url = f"http://{Config.f_open_api}/v1/certs/noaccount/create-cert-task"

        accountDataNew = 获取创建个人或企业的基本信息(使用老数据=使用老数据)
        print(f"\033[91m获取创建个人或企业的基本信息{accountDataNew}\033[0m")
        # accountDataNew = {'姓名': '测试延锦瑾', '手机号': '***********', '身份证号': '610600194911170882', '企业名称': 'esigntest延锦瑾经营的个体工商户',
        #                   '社会编码': '91000000HDPFTJM27Y', '银行卡号': '****************'}
        data = {
            "bizType": bizType,  # 业务方类型 (枚举: TIANYIN,TIANYIN_OFFLINE,ESHIELD,TCLOUD,PUBCLOUD)
            "operatorType": "APPLY",  # 申请类型-（APPLY, DELAY，UPDATE）
            "projectId": Config.appid,
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
            "phone": accountDataNew["手机号"],
            "applyUserModel": {
                "certName": accountDataNew["姓名"] if 个人or企业 == 1 else accountDataNew["企业名称"],
                "licenseNumber": accountDataNew["身份证号"] if 个人or企业 == 1 else accountDataNew["社会编码"],
                "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码
                "userType": 1 if 个人or企业 == 1 else 2,
                "手机号" : accountDataNew["手机号"]

            },
            "authMaterialList": [
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "authLetter"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "license"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "authLetterTemplate"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "idCardFront"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "idCardBack"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "agentIdCardFront"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "agentIdCardBack"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "caCertAgreement"
                },
                {
                    "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
                    "authMaterialContentFormat": "png",
                    "authMaterialContentType": "fileKey",
                    "authMaterialType": "caCertAgreementTemplate"
                }
            ],
            "applyConfigModel":{"certType":"DOUBLE","issuer":"","algorithm":"SM2"}

        }
        # if 个人or企业 == 2: #如果是企业，新加经办人信息
        if 1:
            applyCommonModel={
                "agentType": 1,
                "mobile": accountDataNew["手机号"],
                "agentName": accountDataNew["姓名"],
                "agentLicenseType": 19,
                "agentIdNo": accountDataNew["身份证号"],
                "legalUserIdCard": accountDataNew["身份证号"],
                "legalUserName": accountDataNew["姓名"],
            }
            data["applyCommonModel"] = applyCommonModel

        print(f"\033[91m创建无账号证书申请任务接口,入参是{data}\033[0m")
        method = "POST"
        response = 请求接口(url, method, headers=headers, data=data)
        print(f"创建无账号证书申请任务接口返回信息：{response}")
        taskId = response["data"]["taskId"]
        # 新的写入
        生成的taskid保存和校验(taskId, data, "写入")
    # 老的执行全量巡检
    生成的taskid保存和校验(taskId, data, "读取")

def 更新和延期无账号申请任务接口(更新和延期=1, 个人or企业=1, certId=None, 延期时长="ONEYEAR", bizType="TIANYIN_OFFLINE"):
    if 更新和延期 == 1:
        data = {
            "bizType": bizType,
            "operatorType": "UPDATE",
            "projectId": Config.appid,  # appid
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
            "applyCommonModel": {
                "agentType": 0
            },
            "applyConfigModel": {
                "algorithm": "RSA",
                "certTime": 延期时长,
                "certType": "SINGLE",
                "issuer": "ZHCA",
                "certNum": 1,
                "certId": certId
            },
            "applyUserModel": {
                "userType": 1 if 个人or企业 == 1 else 2,
                "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码
                "certName": "测试更新证书" + random_str() if  个人or企业 == 1 else "esigntest测试更新证书" + random_str(),  #拼接随机汉字

            }
        }
    elif 更新和延期 == 2:
        data = {
            "bizType": bizType,
            "operatorType": "DELAY",
            "projectId": Config.appid,  # appid
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
            "applyCommonModel": {
                "agentType": 0
            },
            "applyConfigModel": {
                "algorithm": "RSA",
                "certTime": "ONEYEAR",
                "certType": "SINGLE",
                "certNum": 1,
                "certId": certId
            },
            "applyUserModel": {
                "userType": 1 if 个人or企业 == 1 else 2,
                "licenseType": 19 if 个人or企业 == 1 else 1,  # 个人一般是9或者19这个是身份证，企业一般是1 这个是统一社会信用代码

            }
        }
    authMaterialList= [
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "authLetter"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "license"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "authLetterTemplate"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "idCardFront"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "idCardBack"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "agentIdCardFront"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "agentIdCardBack"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "caCertAgreement"
        },
        {
            "authMaterialContent": "$e5f6d12d9d074c10a7f37957e0be158e$**********$H",
            "authMaterialContentFormat": "png",
            "authMaterialContentType": "fileKey",
            "authMaterialType": "caCertAgreementTemplate"
        }
    ]
    data["authMaterialList"] = authMaterialList
    url = f"http://{Config.f_open_api}/v1/certs/noaccount/create-cert-task"
    print(f"\033[91m无账号更新和延期证书申请任务接口,入参是{data}\033[0m")
    method = "POST"
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"无账号更新和延期证书申请任务接口：{response}")
    taskId = response["data"]["taskId"]
    # 新的写入
    生成的taskid保存和校验(taskId, data, "写入")
    # 老的执行全量巡检
    生成的taskid保存和校验(taskId, data, "读取")


def 根据certid查询证书关联的oid(certId):
    """
    查询证书ID关联的OID。

    :param certId: 证书ID
    :return: 返回响应数据
    """
    url = f"http://{Config.cert_service}/queryCertUser/model"
    data = {
        "certId": certId
    }
    method = "POST"
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"查询证书关联的OID接口返回信息：{response}")
    return response


def 获取csr(原始数据):
    operatorType = 原始数据["operatorType"]
    证书详情 = ""
    if operatorType in ("UPDATE","DELAY") :
        certId = 原始数据['applyConfigModel']["certId"]
        with open("./证书信息.txt", "r", encoding="utf-8") as f:
            aa = f.readlines()
            if aa:
                for i in aa:
                    if certId in i:
                        证书详情 = i
                        break
    else:
        证书详情 = str(原始数据)  #在apply的时候，加密从入参获取

    url = f"http://{Config.svs_service}/getCsr/request"
    data = {
        "algorithm": "SM2" if "SM2" in 证书详情 else "RSA",
        "dn": {
            "cn": "甘舰戈",
            "ou": "411403199408189070",
            "l": "hz",
            "st": "zj",
            "c": "CN"
        }}
    method = "POST"
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"获取csr入参是：{data}")
    csr = response["csr"]
    return csr


def 生成证书(taskId, 原始数据):
    # 如果是天印，是自动生成证书
    # 如果是zhaoqianbao，需要手动调用生成证书
    operatorType = 原始数据["operatorType"]
    if operatorType == "APPLY":
        url = f"http://{Config.f_open_api}/v1/certs/apply-cert"
    elif operatorType == "UPDATE":
        url = f"http://{Config.f_open_api}/v1/certs/update-cert"
    elif operatorType == "DELAY":
        url = f"http://{Config.f_open_api}/v1/certs/delay-cert"

    data = {
        "csr": 获取csr( 原始数据),
        "extDataMap": {},
        "taskId": taskId
    }

    if operatorType == "UPDATE":
        data["certId"] = 原始数据["applyConfigModel"]["certId"]
    elif operatorType == "DELAY":
        data["certId"] = 原始数据["applyConfigModel"]["certId"]

    method = "POST"
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"：{response}")
    data = response["data"]
    certId = data["certId"]
    algorithm = data["algorithm"]
    startDate = data["startDate"]
    endDate = data["endDate"]
    return data


def 查询证书任务外部接口(taskId):
    url = f"http://{Config.f_open_api}/v1/certs/task/{taskId}"
    method = "GET"
    response = 请求接口(url, method, headers=headers)

    data = response["data"]
    status = data["status"]  # 任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败
    if taskId == "8a52be31a9cb4b609a6cd98219fd49aa":
        print(data)
    if status == 1:
        pass
    elif status == 4 :
        print(f"\033[91m证书任务失败，原因：{response}\033[0m")
    cert = data.get("cert")
    #写到 证书信息.txt里
    if cert:
        with open("证书信息.txt", "a") as f:
            f.write(str(data))
            f.write("\n")

        #读取所有证书信息
        with open("证书信息.txt", "r") as f:
            cert_info = f.readlines()

        allCertId = []
        for i in cert_info:
            if i == "\n" :
                continue
            if i not in allCertId :
                allCertId.append(i.strip() + "\n")  # 强制统一格式，避免旧文件影响

        with open("证书信息.txt", "w") as f:
            for single_cert_info  in allCertId:
                f.write(single_cert_info)

        # if str(data) in cert_info:
        #     pass
        # else:
        #     with open("证书信息.txt", "a") as f:
        #         print(f"没有找到证书信息，执行写入{data}")
        #         f.write(str(data))
        #         f.write("\n")


    # if cert:
    #     certId = cert["certId"]
    #     algorithm = cert["algorithm"]
    #     startDate = cert["startDate"]
    #     endDate = cert["endDate"]
    return data



def 查询证书任务内部接口(taskId):
    url = f"http://{Config.f_open_api}/v1/inner-api/certs/query-task"
    method = "POST"
    data = {
        "taskId": taskId
    }
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"查询证书任务内部接口接口返回信息：{response}")
    data = response["data"]
    status = data["status"]  # 任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败
    taskId = data["taskId"]
    return data


def 生成的taskid保存和校验(taskId="taskId", 造数参数="造数参数", mode="mode"):
    # 获取当前脚本的目录路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "taskId.txt")
    statsEnum = {1: "初始化", 2: "等待制证", 3: "制证完成", 4: "制证失败"}
    更新时间 = datetime.datetime.now()
    if mode == "写入":
        content = [str(更新时间), taskId, 造数参数]
        with open(file_path, "a", encoding="utf-8") as file:
            file.write(str(content))
            file.write("\n")  # 换行
    elif mode == "读取":
        with open(file_path, "r+", encoding="utf-8") as file:  # 既可以读，又可以写
            content = file.readlines()[::-1]  # 倒序读取,只读取每条状态最新的
            checkTaskIdList = []  # 已校验过的taskId，不再重复校验
            for i in content:
                i = eval(i)
                taskId = i[1]
                if taskId == "11d9bac477204f13a82480f2267439ac":
                    print(i)
                if taskId in checkTaskIdList:
                    continue
                else:
                    checkTaskIdList.append(taskId)
                造数参数 = i[2]
                operatorType = 造数参数["operatorType"]  # APPLY=新增, UPDATE=更新, DELAY=延期
                applyUserModel = 造数参数["applyUserModel"]
                if len(i) < 4:  # 如果没有状态，则查询状态
                    data = 查询证书任务外部接口(taskId)
                    status = data["status"]  # 任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败
                    statR = statsEnum.get(status, "未知状态")
                    i.append(statR)
                    file.seek(0, 2)
                    file.write(str(i))
                    file.write("\n")
                else:
                    制证状态 = i[3]
                    if 制证状态 == "制证完成":
                        pass

                    else:  # 如果不是完成，则查询状态,并制证完成
                        data = 查询证书任务外部接口(taskId)
                        status = data["status"]  # 任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败
                        statR = statsEnum.get(status, "未知状态")
                        if statR == "制证失败":
                            print(f"\033[91m任务{taskId}状态为{statR}，请检查\033[0m")                            # raise TaskStatusException(f"任务 {taskId} 状态为 {statR}，请检查")
                        if statR == "制证完成":
                            # print(f"天印任务{taskId}状态为{statR}，不需要再校验")
                            if "TIANYIN" in str(i[2]):
                                print(f"TIANYIN is ok{taskId}")
                            else:
                                raise TaskStatusException(f"天印任务 {taskId} 状态为 {statR}，请检查")


                        if statR == "等待制证":
                            print(f"开始校验数据是：{i}")

                            if operatorType == "APPLY":
                                oid = applyUserModel.get("oid")
                                if oid: # 如果oid不为空，则不校验是否是实名组织
                                    pass
                                else:
                                    userType = applyUserModel["userType"]
                                    certName = applyUserModel["certName"]
                                    print("开始校验企业和个人是否是实名组织")
                                    res = 获取个人或企业的实名组织(userType, certName)
                                    if res:
                                        pass
                                    else:
                                        print(f"\033[91m实名组织校验失败{certName}\033[0m")


                            try:
                                data = 生成证书(taskId, 造数参数)  # 调用制证接口
                                certId1 = data["certId"]
                                algorithm1 = data["algorithm"]
                                startDate1 = data["startDate"]
                                endDate1 = data["endDate"]
                                time.sleep(0.5)
                                i.append({"制证完成":certId1})
                                file.seek(0, 2)
                                file.write(str(i))
                                file.write("\n")
                                # 制证完成后，查询状态
                                dataQ = 查询证书任务外部接口(taskId)
                                status = dataQ["status"]  # 任务状态 1-初始化, 2-等待制证 3-制证完成 4-制证失败
                                statR = statsEnum.get(status, "未知状态")

                                cert = dataQ.get("cert")[0]
                                certId2 = cert["certId"]
                                algorithm2 = cert["algorithm"]
                                startDate2 = cert["startDate"]
                                endDate2 = cert["endDate"]
                                if statR == "制证完成":
                                    # certid、algorithm、startDate、endDate 要相等
                                    assert certId1 == certId2
                                    assert algorithm1 == algorithm2
                                    assert startDate1 == startDate2
                                    assert endDate1 == endDate2

                                    # oid下没有证书
                                    """
                                    1、查询oid
                                    2、根据oid查询certid
                                    
                                    """
                                    res = 根据certid查询证书关联的oid(certId1)
                                    data = res["users"]
                                    assert len(data) == 0

                                    if operatorType == "UPDATE":
                                        pass
                                    elif operatorType == "DELAY":
                                        pass

                                else:
                                    print(f"任务{taskId}状态为{statR}，请检查")
                                    raise TaskStatusException(f"任务 {taskId} 状态为 {statR}，请检查")
                            except Exception as e:
                                print(f"任务{taskId}状态为{statR}，请检查")
                                print(f"\033[91m{dataQ}{e}\033[0m")

                                # raise TaskStatusException(f"任务 {taskId} 状态为 {statR}，请检查")

def 吊销证书(certId):
    url = f"http://{Config.esignra}/standardCertRevoke/request"
    method = "POST"
    data = {
	"appId": Config.appid,
	"certId":certId
}
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"吊销证书任务内部接口接口返回信息：{response}")
    return data

def 重发证书(certId):
    url = f"http://{Config.esignra}/standardCertResign/request"
    method = "POST"
    data = {
	"certId": certId,
	"csr":  "",
	"ukeyNumber": ""
}
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"重发证书任务内部接口接口返回信息：{response}")
    return data

def 查询协议(certId):
    url = f"http://{Config.esignra}/standardQueryAgreementByCertId/request"
    method = "POST"
    data = {
	"certId": certId,
	"csr":  "",
	"ukeyNumber": ""
}
    response = 请求接口(url, method, headers=headers, data=data)
    print(f"查询协议内部接口接口返回信息：{response}")
    return data



if __name__ == '__main__':
    class 法人():
        signerAccountId = "713e3c56d0b24b8d98766693d6e675b8"
        psnAccount = "***********"


    class 管理员():
        signerAccountId = "24410509f77448fcb8cab2951ea853b6"
        psnAccount = "***********"


    class 普通个人():
        signerAccountId = "********************************"
        psnAccount = "***********"


    class 企业():
        authorizedAccountId = "90fc72de45174e66b70653c7ec1f7aae"


    class 非实名个人():
        signerAccountId = "c4b4b21c3a014c73a078617d4928757e"
        psnAccount = "***********"

    # 查询证书任务外部接口("504e852d55504b74bd13e867d4d02e49")
    #甘舰戈  25c300258dfb4d1b9d2aa2d0cf54f44b


    # 创建证书申请任务接口(个人or企业=1, oid="", 产品=[2],使用老数据=1)  # 1、TIANYIN、2、TCLOUD 3、ESHIELD
    生成的taskid保存和校验(mode="读取")
    # 更新1，延期2\
    # 更新和延期证书申请任务接口(更新和延期=2, 个人or企业=1, certId="49a15ed3a24d4c10b113bce646b73d83 ",
    #                            延期时长="ONEYEAR", bizType="TCLOUD")
    # 生成的taskid保存和校验(mode="读取")


    # 创建无账号证书申请任务接口(个人or企业=2,  产品=[2],使用老数据=1) # 1、TIANYIN_OFFLINE  2、PUBCLOUD 3、tcloud 4、ESHIELD
    # 生成的taskid保存和校验(mode="读取")

    # 无账号更新
    # 更新和延期无账号申请任务接口(更新和延期=1, 个人or企业=1, certId="49a15ed3a24d4c10b113bce646b73d83 ",延期时长="ONEYEAR", bizType="TIANYIN_OFFLINE")
    # 生成的taskid保存和校验(mode="读取")




    # 吊销证书("f649fe26472c4e32ba72b8f7503f3979")
    # 重发证书("70d0a66dafa74b119acd7d8f3c7dfcb6")
    # 查询协议("88c1d49b517146a0bf1e532689284989")