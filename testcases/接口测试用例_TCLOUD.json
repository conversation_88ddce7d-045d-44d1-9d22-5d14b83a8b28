[{"用例标题": "缺少bizType参数", "用例入参": {"operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: bizType"}, {"用例标题": "缺少operatorType参数", "用例入参": {"bizType": "TCLOUD", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: operatorType"}, {"用例标题": "缺少projectId参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: projectId"}, {"用例标题": "缺少userModel.certName参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: userModel.certName"}, {"用例标题": "缺少userModel.licenseNumber参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: userModel.licenseNumber"}, {"用例标题": "缺少userModel.licenseType参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "userType": 2}}, "用例结果": "缺少必要参数: userModel.licenseType"}, {"用例标题": "缺少userModel.userType参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1}}, "用例结果": "缺少必要参数: userModel.userType"}, {"用例标题": "续期申请时缺少certId参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "DELAY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: certId"}, {"用例标题": "更新申请时缺少certId参数", "用例入参": {"bizType": "TCLOUD", "operatorType": "UPDATE", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19888644846", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest苍诚经营的个体工商户", "licenseNumber": "91000000BL0X3BDKXJ", "licenseType": 1, "userType": 2}}, "用例结果": "缺少必要参数: certId"}]