- config:
    name: 创建有账号证书申请任务
    variables:
      appId: ${ENV(appid1)}
      appid: ${ENV(appid)}
      bizType: TCLOUD
      operatorType: APPLY
      projectId: **********
      notifyUrl: http://libaohui.com.cn/callback/ding
      redirectUrl: https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai
      phone: 19888644846
      applyConfigModel:
        certNum: 1
      applyCommonModel: {}
      applyUserModel:
        certName: esigntest苍诚经营的个体工商户
        licenseNumber: 91000000BL0X3BDKXJ
        licenseType: 1
        userType: 2

- test:
    name: 正向用例-bizType枚举值_TIANYIN
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "bizType": "TIANYIN",
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "用户类型和传入的用户信息不一致"]

- test:
    name: 反向用例-bizType非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "INVALID_ENUM",
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "No enum constant com.timevale.esignra.facade.enums.CertTaskBizTypeEnum.INVALID_ENUM"]

- test:
    name: 正向用例-operatorType枚举值_APPLY
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": "APPLY",
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 正向用例-operatorType枚举值_UPDATE
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": "UPDATE",
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "证书id不能为空"]

- test:
    name: 正向用例-operatorType枚举值_DELAY
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": "DELAY",
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "证书id不能为空"]

- test:
    name: 反向用例-operatorType非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": "INVALID_ENUM",
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "No enum constant com.timevale.esignra.facade.enums.OperationTypeEnum.INVALID_ENUM"]

- test:
    name: 正向用例-userType枚举值_1
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel":
            {
              "certName": "esigntest苍诚经营的个体工商户",
              "licenseNumber": "91000000BL0X3BDKXJ",
              "licenseType": 1,
              "userType": 1
            }
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "用户类型和传入的用户信息不一致"]

- test:
    name: 正向用例-userType枚举值_2
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel":
            {
              "certName": "esigntest苍诚经营的个体工商户",
              "licenseNumber": "91000000BL0X3BDKXJ",
              "licenseType": 1,
              "userType": 2
            }
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 反向用例-userType非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel":
            {
              "certName": "esigntest苍诚经营的个体工商户",
              "licenseNumber": "91000000BL0X3BDKXJ",
              "licenseType": 1,
              "userType": 3
            }
        }
    validate:
      - eq: ["content.code", 1451001]
      - contains: ["content.message", "用户类型和传入的用户信息不一致"]

- test:
    name: 正向用例-agentType枚举范围_1
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel":
            {
              "agentType": 1
            },
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 正向用例-agentType枚举范围_2
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel":
            {
              "agentType": 2
            },
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]


- test:
    name: 正向用例-algorithm枚举值_RSA
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "algorithm": "RSA"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 正向用例-algorithm枚举值_SM2
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "algorithm": "SM2"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 反向用例-algorithm非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "algorithm": "INVALID_ENUM"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 反向用例-agentType非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel":
            {
              "agentType": 3
            },
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]


- test:
    name: 正向用例-certType枚举值_SINGLE
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "certType": "SINGLE"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 正向用例-certType枚举值_DOUBLE
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "certType": "DOUBLE"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]

- test:
    name: 反向用例-certType非法值
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel":
            {
              "certType": "INVALID_ENUM"
            },
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", "成功"]