- config:
    name: 搜索关键字
    variables:
      appId: ${ENV(appid1)}
      fileId1: 1eb4f0c926a240b781d862dd839bac6b


- test:
    name: 搜索关键字-fileId不存在
    api: api/files/keyword-positions.yml
    variables:
      json:
        { 
          "fileId": "123",  
          "keywords": ["盖章"],
          "appId": $appId
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "查询文档信息失败,合同不存在"]


- config:
    name: 创建有账号证书申请任务
    variables:
      bizType: TCLOUD
      operatorType: APPLY
      projectId: **********
      notifyUrl: http://libaohui.com.cn/callback/ding
      redirectUrl: https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai
      phone: 19888644846
      applyConfigModel:
        certNum: 1
      applyCommonModel: {}
      applyUserModel:
        certName: esigntest苍诚经营的个体工商户
        licenseNumber: 91000000BL0X3BDKXJ
        licenseType: 1
        userType: 2

- test:
    name: 正向用例-所有必填参数正确
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", ""]

- test:
    name: 缺失基础必填参数-bizType
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", -1]
      - eq: ["content.message", "参数错误: bizType不能为空"]

- test:
    name: 缺失基础必填参数-operatorType
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel": $applyUserModel
        }
    validate:
      - eq: ["content.code", -1]
      - eq: ["content.message", "参数错误: operatorType不能为空"]

- test:
    name: 缺失基础必填参数-applyUserModel
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel
        }
    validate:
      - eq: ["content.code", -1]
      - eq: ["content.message", "参数错误: applyUserModel不能为空"]

- test:
    name: 缺失嵌套必填参数-userType
    api: /v1/certs/create-cert-task.yml
    variables:
      json:
        {
          "bizType": $bizType,
          "operatorType": $operatorType,
          "projectId": $projectId,
          "notifyUrl": $notifyUrl,
          "redirectUrl": $redirectUrl,
          "phone": $phone,
          "applyConfigModel": $applyConfigModel,
          "applyCommonModel": $applyCommonModel,
          "applyUserModel":
            {
              "certName": "esigntest苍诚经营的个体工商户",
              "licenseNumber": "91000000BL0X3BDKXJ",
              "licenseType": 1
            }
        }
    validate:
      - eq: ["content.code", -1]
      - eq: ["content.message", "参数错误: applyUserModel.userType不能为空"]

