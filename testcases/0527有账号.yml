- config:
    name: TCLOUD证书申请任务测试
    variables:
      appId: ${ENV(appid1)}
      appid: ${ENV(appid)}
      personalTaskId: ""
      enterpriseTaskId: ""

# 个人证书申请
- test:
    name: 创建TCLOUD个人证书任务
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        bizType: "TCLOUD"
        operatorType: "APPLY"
        projectId: "$appId"
        notifyUrl: "http://libaohui.com.cn/callback/ding"
        redirectUrl: "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai"
        phone: "19888644846"
        applyConfigModel:
          certNum: 1
          algorithm: "RSA"
          certType: "SINGLE"
          issuer: "ZHCA"
        applyCommonModel:
          agentType: 0
        applyUserModel:
          certName: "测试个人证书"
          licenseNumber: "110101199003077654"
          licenseType: 19
          userType: 1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
    extract:
      - personalTaskId: content.data.taskId
 #查询个人证书任务状态
- test:
    name: 查询TCLOUD个人证书任务状态
    api: api/根据taskid查询任务状态.yml
    method: GET
    variables:
        taskId: $personalTaskId
        operatorType: "QUERY"
        projectId: "$appId"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.taskId", "$personalTaskId"]

 #企业证书申请
- test:
    name: 创建TCLOUD企业证书任务
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        bizType: "TCLOUD"
        operatorType: "APPLY"
        projectId: "$appId"
        notifyUrl: "http://libaohui.com.cn/callback/ding"
        redirectUrl: "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai"
        phone: "19888644846"
        applyConfigModel:
          certNum: 1
          algorithm: "RSA"
          certType: "SINGLE"
          issuer: "ZHCA"
        applyCommonModel:
          agentType: 0
        applyUserModel:
          certName: "测试企业证书"
          licenseNumber: "91310104MA1FRP2X2"
          licenseType: 1
          userType: 2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
    extract:
      - enterpriseTaskId: content.data.taskId



 #查询企业证书任务状态
- test:
    name: 查询TCLOUD企业证书任务状态
    api:  api/根据taskid查询任务状态.yml
    method: GET
    variables:
      taskId: $enterpriseTaskId
      operatorType: "QUERY"
      projectId: "$appId"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.taskId", "$enterpriseTaskId"]