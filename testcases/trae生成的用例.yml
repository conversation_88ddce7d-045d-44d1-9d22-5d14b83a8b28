- config:
    name: 创建有账号证书申请任务接口测试用例
    variables:
      appId: ${ENV(appid1)}
      appid: ${ENV(appid)}
      baseRequest: &baseRequest
        bizType: 'TCLOUD'
        operatorType: 'APPLY'
        projectId: '**********'
        notifyUrl: 'http://libaohui.com.cn/callback/ding'
        redirectUrl: 'https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai'
        phone: '19888644846'
        applyConfigModel:
          certNum: 1
        applyCommonModel: {}
        applyUserModel:
          certName: 'esigntest苍诚经营的个体工商户'
          licenseNumber: '91000000BL0X3BDKXJ'
          licenseType: 1
          userType: 2

# 正向用例
- test:
    name: 创建有账号证书申请任务 - 正向用例
    api: api/创建证书接口.yml
    method: POST
    variables:
      json: *baseRequest
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

# 基础必填参数缺失场景
- test:
    name: 创建有账号证书申请任务 - 缺失 bizType
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        bizType: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: bizType 为必填参数"]

- test:
    name: 创建有账号证书申请任务 - 缺失 operatorType
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        operatorType: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: operatorType 为必填参数"]

- test:
    name: 创建有账号证书申请任务 - 缺失 applyUserModel
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        applyUserModel: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: applyUserModel 为必填参数"]

- test:
    name: 创建有账号证书申请任务 - 缺失 applyUserModel.userType
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        applyUserModel:
          certName: 'esigntest苍诚经营的个体工商户'
          licenseNumber: '91000000BL0X3BDKXJ'
          licenseType: 1
          userType: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: applyUserModel.userType 为必填参数"]

# 条件必填参数缺失场景（申请类型为续期和更新时，certId 为必填）
- test:
    name: 创建有账号证书申请任务 - 申请类型为 DELAY 时缺失 certId
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        operatorType: 'DELAY'
        applyConfigModel:
          certNum: 1
          certId: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: 当申请类型为 DELAY 或 UPDATE 时，certId 为必填参数"]

- test:
    name: 创建有账号证书申请任务 - 申请类型为 UPDATE 时缺失 certId
    api: api/创建证书接口.yml
    method: POST
    variables:
      json:
        <<: *baseRequest
        operatorType: 'UPDATE'
        applyConfigModel:
          certNum: 1
          certId: null
    validate:
      - eq: ["status_code", 400]
      - eq: ["content.message", "参数错误: 当申请类型为 DELAY 或 UPDATE 时，certId 为必填参数"]