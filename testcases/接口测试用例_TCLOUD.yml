- config:
    name: 接口测试
    variables:
      appid: ${ENV(appid)}


- test:
    name: 缺少bizType参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "operatorType": "APPLY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451000]
      - eq: ["content.message", "参数错误: bizType不能为空"]

- test:
    name: 缺少operatorType参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451000]
      - eq: ["content.message", "参数错误: operatorType不能为空"]

- test:
    name: 缺少projectId参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "APPLY",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 缺少userModel.certName参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "APPLY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "oid和用户信息不能同时为空"]

- test:
    name: 缺少userModel.licenseNumber参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "APPLY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: [ "content.code", 1451001 ]
      - eq: [ "content.message", "oid和用户信息不能同时为空" ]

- test:
    name: 缺少userModel.licenseType参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "APPLY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451001]
#      - eq: ["content.message", "缺少必要参数: userModel.licenseType"]

- test:
    name: 缺少userModel.userType参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "APPLY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1
          }
        }
    validate:
      - eq: [ "content.code", 1451001 ]
      #      - eq: ["content.message", "缺少必要参数: userModel.licenseType"]

- test:
    name: 续期申请时缺少certId参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "DELAY",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "证书id不能为空"]

- test:
    name: 更新申请时缺少certId参数
    api: api/创建证书接口.yml
    variables:
      json:
        {
          "bizType": "TCLOUD",
          "operatorType": "UPDATE",
          "projectId": "**********",
          "notifyUrl": "http://libaohui.com.cn/callback/ding",
          "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
          "phone": "19888644846",
          "applyConfigModel": {
            "certNum": 1
          },
          "applyCommonModel": {},
          "applyUserModel": {
            "certName": "esigntest苍诚经营的个体工商户",
            "licenseNumber": "91000000BL0X3BDKXJ",
            "licenseType": 1,
            "userType": 2
          }
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "证书id不能为空"]
