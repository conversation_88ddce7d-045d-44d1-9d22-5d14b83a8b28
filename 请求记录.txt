{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "19834335136", "applyConfigModel": {"certNum": 1, "certType": "", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest闻荣经营的个体工商户", "licenseNumber": "91000000PPJL98W95J", "licenseType": 1, "userType": 2}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://smlt.esign.cn/rPjpuuu\",\"taskId\":\"82356fc78b854444b78c79b254e0e083\",\"taskLongUrl\":\"https://aaaaaaa.smlh5.esign.cn/auth/caRA?taskId=82356fc78b854444b78c79b254e0e083\"}}"}
{"url": "http://sdk.testk8s.tsign.cn/random/get", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"env": "模拟环境", "mock": true, "total": 1}, "响应": "{\"success\":true,\"accountList\":[{\"orgCode\":\"91000000M8QW3NCH0B\",\"idNo\":\"130803199701250305\",\"name\":\"测试雍娜\",\"englishName\":\"Hazel Ellen Elliott\",\"bankCard\":\"6213460693184681367\",\"phone\":\"***********\",\"orgName\":\"esigntest雍娜经营的个体工商户\"}],\"message\":\"仅mock验证码，会持续发送短信！\"}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TIANYIN", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "***********", "applyConfigModel": {"certNum": 1, "certType": "", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest雍娜经营的个体工商户", "licenseNumber": "91000000M8QW3NCH0B", "licenseType": 1, "userType": 2}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://smlt.esign.cn/uVYJPCc\",\"taskId\":\"ca762bef29b742269e7000ac5d8919b7\",\"taskLongUrl\":\"https://aaaaaaa.smlh5.esign.cn/auth/caRA?taskId=ca762bef29b742269e7000ac5d8919b7\"}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/ca762bef29b742269e7000ac5d8919b7", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"ca762bef29b742269e7000ac5d8919b7\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/82356fc78b854444b78c79b254e0e083", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"82356fc78b854444b78c79b254e0e083\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/3c297f5f4bf24fb5afc96d0124b7f9f8", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"3c297f5f4bf24fb5afc96d0124b7f9f8\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/99617d2de38c4549a44e59a5daed2275", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"99617d2de38c4549a44e59a5daed2275\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/c145e8734d3f482ea71c5dacee2284e3", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"c145e8734d3f482ea71c5dacee2284e3\",\"status\":2,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://svs-service.testk8s.tsign.cn/getCsr/request", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"algorithm": "RSA", "dn": {"cn": "甘舰戈", "ou": "411403199408189070", "l": "hz", "st": "zj", "c": "CN"}}, "响应": "{\"success\":true,\"message\":\"执行成功\",\"csr\":\"MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCJ6MYrICF+g1iHtEis8UFqtovca0S9I/e3EWxM7TZnXGiidM/WMByr8ikxnsrcczVf4ZyYXG2FdDCaviT1p2YLzKf/OCAQA4yya9u+RhQlPcVVNd584jVBsnK2Zy4GsHnEfxypBUVHIVuXVW6l19togK7gGJ32B6jYIgoDFrzNs0XO88j7se1mDk8h0HpSsfKk5OmwyORdfKrEgld8fjCAx+VNvsUOuVL04Csl0U5Xn2eOLUSBG2Eny/xmJ4t7oInfuh412+oH40AGKbtxsiVPTIgTaOWMgX0RE1CkQqGMMA53mT7h523KBUQFhmNiBT2cR57gcU1k01OjQ3cTt8RJAgMBAAEwDQYJKoZIhvcNAQELBQADggEBADqppcjUSg2AIrYbhDwh+Ma9kEFgCCORLFtfubZl9sMewl3uaNiMcDHf4RL3oT1t1q6KtXO9fV/OdYAcPgxPxQVDehI0TIx7aPrV7Q/RtxYn2Y//wI8GK3nTzYJ+hM/9o+hHHR0jvXqpKBqy/kVotIRBChevn3upGvMLKgZvPv09I/oBOFKVzNihXtLlIdckKpPAeM1nReLQWDb6oae9BPLbJHOSuSR2U0RLytw6aAhACX1A9ICMj2OG73ftCIplj+jcLanZ5XVm2HBvjHrFPYIABBS1spiPp/34yrumiFCZ7Lhalc4YshVtCVqcp/G1lrJurEYX1KB2qjAaWpbh0xo=\\n\",\"svsId\":\"3469af05-7446-4002-acb4-ce7ca77b1759\"}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/apply-cert", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"csr": "MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCJ6MYrICF+g1iHtEis8UFqtovca0S9I/e3EWxM7TZnXGiidM/WMByr8ikxnsrcczVf4ZyYXG2FdDCaviT1p2YLzKf/OCAQA4yya9u+RhQlPcVVNd584jVBsnK2Zy4GsHnEfxypBUVHIVuXVW6l19togK7gGJ32B6jYIgoDFrzNs0XO88j7se1mDk8h0HpSsfKk5OmwyORdfKrEgld8fjCAx+VNvsUOuVL04Csl0U5Xn2eOLUSBG2Eny/xmJ4t7oInfuh412+oH40AGKbtxsiVPTIgTaOWMgX0RE1CkQqGMMA53mT7h523KBUQFhmNiBT2cR57gcU1k01OjQ3cTt8RJAgMBAAEwDQYJKoZIhvcNAQELBQADggEBADqppcjUSg2AIrYbhDwh+Ma9kEFgCCORLFtfubZl9sMewl3uaNiMcDHf4RL3oT1t1q6KtXO9fV/OdYAcPgxPxQVDehI0TIx7aPrV7Q/RtxYn2Y//wI8GK3nTzYJ+hM/9o+hHHR0jvXqpKBqy/kVotIRBChevn3upGvMLKgZvPv09I/oBOFKVzNihXtLlIdckKpPAeM1nReLQWDb6oae9BPLbJHOSuSR2U0RLytw6aAhACX1A9ICMj2OG73ftCIplj+jcLanZ5XVm2HBvjHrFPYIABBS1spiPp/34yrumiFCZ7Lhalc4YshVtCVqcp/G1lrJurEYX1KB2qjAaWpbh0xo=\n", "extDataMap": {}, "taskId": "c145e8734d3f482ea71c5dacee2284e3"}, "响应": "{\"code\":1451001,\"message\":\"任务中的appid与请求头中的appid不一致,无法制证\",\"data\":null}"}
{"url": "http://sdk.testk8s.tsign.cn/random/get", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"env": "测试环境", "mock": true, "total": 1}, "响应": "{\"success\":true,\"accountList\":[{\"orgCode\":\"91000000YC11CTPLXK\",\"idNo\":\"36030219540327377X\",\"name\":\"测试贾武\",\"englishName\":\"Calvin James\",\"bankCard\":\"621106088199431744\",\"phone\":\"***********\",\"orgName\":\"esigntest贾武经营的个体工商户\"}],\"message\":\"仅mock验证码，会持续发送短信！\"}"}
{"url": "http://in-test-openapi.tsign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TIANYIN", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "***********", "applyConfigModel": {"certNum": 1, "certType": "", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest贾武经营的个体工商户", "licenseNumber": "91000000YC11CTPLXK", "licenseType": 1, "userType": 2}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://testt.tsign.cn/UfnKm7v\",\"taskId\":\"8342c8d80b2f45189df4ea8b39f93379\",\"taskLongUrl\":\"https://aa.ganning.testh5.tsign.cn/auth/caRA?taskId=8342c8d80b2f45189df4ea8b39f93379\"}}"}
{"url": "http://in-test-openapi.tsign.cn/v1/certs/8342c8d80b2f45189df4ea8b39f93379", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":110,\"message\":\"证书不存在\",\"data\":null}"}
{"url": "http://sdk.testk8s.tsign.cn/random/get", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"env": "模拟环境", "mock": true, "total": 1}, "响应": "{\"success\":true,\"accountList\":[{\"orgCode\":\"91000000NEFP70W6XP\",\"idNo\":\"******************\",\"name\":\"测试正亚\",\"englishName\":\"Evelyn Holly Kennedy\",\"bankCard\":\"6232727583532015096\",\"phone\":\"***********\",\"orgName\":\"esigntest正亚经营的个体工商户\"}],\"message\":\"仅mock验证码，会持续发送短信！\"}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TIANYIN", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "***********", "applyConfigModel": {"certNum": 1, "certType": "", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "esigntest正亚经营的个体工商户", "licenseNumber": "91000000NEFP70W6XP", "licenseType": 1, "userType": 2}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://smlt.esign.cn/08OqJ92\",\"taskId\":\"01bc4639867049dd8e7854bdd33cd53d\",\"taskLongUrl\":\"https://aaaaaaa.smlh5.esign.cn/auth/caRA?taskId=01bc4639867049dd8e7854bdd33cd53d\"}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/01bc4639867049dd8e7854bdd33cd53d", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"01bc4639867049dd8e7854bdd33cd53d\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/8342c8d80b2f45189df4ea8b39f93379", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":1451001,\"message\":\"任务信息有问题\",\"data\":null}"}
{"url": "http://footstone-open-api.smlk8s.esign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "applyConfigModel": {"certNum": 1}, "applyCommonModel": {}, "applyUserModel": {"userType": 1, "oid": "25c300258dfb4d1b9d2aa2d0cf54f44b"}}, "响应": "{\"code\":1451001,\"message\":\"open user不存在. ouid:25c300258dfb4d1b9d2aa2d0cf54f44b\",\"data\":null}"}
{"url": "http://sdk.testk8s.tsign.cn/random/get", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"env": "模拟环境", "mock": true, "total": 1}, "响应": "{\"success\":true,\"accountList\":[{\"orgCode\":\"910000005G722Q4077\",\"idNo\":\"640381197010154413\",\"name\":\"测试司楠福\",\"englishName\":\"Todd Micheal Austin\",\"bankCard\":\"6215167046859697567\",\"phone\":\"***********\",\"orgName\":\"esigntest司楠福经营的个体工商户\"}],\"message\":\"仅mock验证码，会持续发送短信！\"}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "***********", "applyConfigModel": {"certNum": 1, "certType": "", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "测试司楠福", "licenseNumber": "640381197010154413", "licenseType": 19, "userType": 1}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://smlt.esign.cn/iH6anbJ\",\"taskId\":\"ad1b264f88154758b18bb883e3d1bf2e\",\"taskLongUrl\":\"https://aaaaaaa.smlh5.esign.cn/auth/caRA?taskId=ad1b264f88154758b18bb883e3d1bf2e\"}}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":110,\"message\":\"证书不存在\",\"data\":null}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":110,\"message\":\"证书不存在\",\"data\":null}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":110,\"message\":\"证书不存在\",\"data\":null}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"ad1b264f88154758b18bb883e3d1bf2e\",\"status\":2,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"ad1b264f88154758b18bb883e3d1bf2e\",\"status\":2,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://svs-service.testk8s.tsign.cn/getCsr/request", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"algorithm": "RSA", "dn": {"cn": "甘舰戈", "ou": "411403199408189070", "l": "hz", "st": "zj", "c": "CN"}}, "响应": "{\"success\":true,\"message\":\"执行成功\",\"csr\":\"MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC1mCymmUzF3fBE2MXp5hEl+ydqGWizL5kohL7xEgnXU6810Cg8n2lV9ytHxMDLQXd975+PdkA4rJ5uJbMMj4ZVhEQCkWwMfQ7rhgct4wW1v9bGigMEDrubfpRqtnMSyYl1FDDmOOOxcB1nNnCEPzZWCfYxg9P7G7P+EawlYOvcKsxL3f4ckaFvpRA89LX+HxYJOfeLvy/PqsPr3l5WZXQhlOLbupbbMGYU65aFjJ6Fpd5WBwsE3uu6oaUCS2PTzPkWeEiS9qrhn1D3o8bhhLR+xGUpDWNGVoydpzdM28ylQSOLKTUAdqyg59NCVg689H0huSho8HgY4QHxAO5Qi6oNAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHfqDXMtf9G6k/uXpIZhF1bZ4yxWyoRxargu071NL5P9UopvILkGi4kKYRZ6c1zRt8s5lAu1XjmSnjpDD2NPdum7CdEAgHbRd0DfxexhuQA1NA+2VzBS0gwU+PNw0lGIYu0k/V8kJJR7VRKNPjUSh4pw2DO7FtMX7K/D7HqafQ/YwDHfxJdpIiRN1Ca8YIR6Ylxo1/HKQp6TvHphMrvPZzy2XUvCsL0O22MzIiFSruvg8h+S6BANI2u9i4glZeMaKQI+evawH+kZ2ZCiYLmi19wX8K0PmyxETze31IdLrg9V9eCbTZeJw2YgYD4VYApzi7tY7ANvdaavksYb/6RdAR0=\\n\",\"svsId\":\"0aa84cce-05e7-482f-8758-82f6e7eef618\"}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/apply-cert", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"csr": "MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC1mCymmUzF3fBE2MXp5hEl+ydqGWizL5kohL7xEgnXU6810Cg8n2lV9ytHxMDLQXd975+PdkA4rJ5uJbMMj4ZVhEQCkWwMfQ7rhgct4wW1v9bGigMEDrubfpRqtnMSyYl1FDDmOOOxcB1nNnCEPzZWCfYxg9P7G7P+EawlYOvcKsxL3f4ckaFvpRA89LX+HxYJOfeLvy/PqsPr3l5WZXQhlOLbupbbMGYU65aFjJ6Fpd5WBwsE3uu6oaUCS2PTzPkWeEiS9qrhn1D3o8bhhLR+xGUpDWNGVoydpzdM28ylQSOLKTUAdqyg59NCVg689H0huSho8HgY4QHxAO5Qi6oNAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHfqDXMtf9G6k/uXpIZhF1bZ4yxWyoRxargu071NL5P9UopvILkGi4kKYRZ6c1zRt8s5lAu1XjmSnjpDD2NPdum7CdEAgHbRd0DfxexhuQA1NA+2VzBS0gwU+PNw0lGIYu0k/V8kJJR7VRKNPjUSh4pw2DO7FtMX7K/D7HqafQ/YwDHfxJdpIiRN1Ca8YIR6Ylxo1/HKQp6TvHphMrvPZzy2XUvCsL0O22MzIiFSruvg8h+S6BANI2u9i4glZeMaKQI+evawH+kZ2ZCiYLmi19wX8K0PmyxETze31IdLrg9V9eCbTZeJw2YgYD4VYApzi7tY7ANvdaavksYb/6RdAR0=\n", "extDataMap": {}, "taskId": "ad1b264f88154758b18bb883e3d1bf2e"}, "响应": "{\"code\":1451001,\"message\":\"系统异常\",\"data\":null}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/create-cert-task", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"bizType": "TCLOUD", "operatorType": "APPLY", "projectId": "**********", "notifyUrl": "http://libaohui.com.cn/callback/ding", "redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai", "phone": "***********", "applyConfigModel": {"certNum": 1, "certType": "SINGLE", "issuer": "GUOXINCA", "algorithm": "RSA"}, "applyCommonModel": {}, "applyUserModel": {"certName": "测试司楠福", "licenseNumber": "640381197010154413", "licenseType": 19, "userType": 1}}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskUrl\":\"https://smlt.esign.cn/OJNS2xU\",\"taskId\":\"f610147c9ade437283988ea2c20db574\",\"taskLongUrl\":\"https://aaaaaaa.smlh5.esign.cn/auth/caRA?taskId=f610147c9ade437283988ea2c20db574\"}}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/f610147c9ade437283988ea2c20db574", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"f610147c9ade437283988ea2c20db574\",\"status\":1,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/ad1b264f88154758b18bb883e3d1bf2e", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"ad1b264f88154758b18bb883e3d1bf2e\",\"status\":2,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://svs-service.testk8s.tsign.cn/getCsr/request", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"algorithm": "RSA", "dn": {"cn": "甘舰戈", "ou": "411403199408189070", "l": "hz", "st": "zj", "c": "CN"}}, "响应": "{\"success\":true,\"message\":\"执行成功\",\"csr\":\"MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCTXwNMSfOdP8mY9Z98eKEsOfDDR3Q2EABZpio6BfsHPOU1NxSEKeAtiNaiTF0Dmk5gQ8PnpftO8K2JdgkuKJ7GZvqI7YUT3BZVs6AHsLRqhRdb0v+WHAdVBQZgBz7e5n8XhMz4ZszeXHROVi6KDOLcSckjRk3wtHzCIO44SSEbLmH8KJwxxLqJa3U4MHkVfW5RgKqAFKXmSJ/1LdjMRGrZwf6i4p5COd9+A5NUjVYZLolpNbTahFDs/iXm0V4APh595fBIXPoV0JWa0zqrA5mKdo0fboStM78h4l9MF42pTTlitvVqmpFpPnjvzJyxCRIUYirb1rYMMpycBJQ8Qyh7AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAAW/lWYj+MJWx0+fk1OeYwEg1IXlCsXeedfsbjRHlwJpbbSx9lgIk3PP16sPlYY73hgsvbymgLRdIe+rHK9PTXnJlIGXsHnJ3Ocdh7L82VnAzcopLwIKJ5e8Gc9xaopfIkVRPJCuXpiTS3s+NYTl55+pX2I4mo/FeOF5UNtlabl+rq9b22s/MASFM6PSGz13QQ4EcBUfV6IOPsFLpo3B2yVPUN3c/Qq5q68owyAT0lLVMyAfIHXr3u4PiEZmJ5uxtVjAUFrtgs2FFNwUWvVWOuP32ENYd9Dp/IckWfx6j9eMQXOPPn4g8OboSRGba1CTh+qt2rvIpnC7MafrVb577wk=\\n\",\"svsId\":\"1d7a979c-5058-4184-b9e4-680155d6ff37\"}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/apply-cert", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"csr": "MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCTXwNMSfOdP8mY9Z98eKEsOfDDR3Q2EABZpio6BfsHPOU1NxSEKeAtiNaiTF0Dmk5gQ8PnpftO8K2JdgkuKJ7GZvqI7YUT3BZVs6AHsLRqhRdb0v+WHAdVBQZgBz7e5n8XhMz4ZszeXHROVi6KDOLcSckjRk3wtHzCIO44SSEbLmH8KJwxxLqJa3U4MHkVfW5RgKqAFKXmSJ/1LdjMRGrZwf6i4p5COd9+A5NUjVYZLolpNbTahFDs/iXm0V4APh595fBIXPoV0JWa0zqrA5mKdo0fboStM78h4l9MF42pTTlitvVqmpFpPnjvzJyxCRIUYirb1rYMMpycBJQ8Qyh7AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAAW/lWYj+MJWx0+fk1OeYwEg1IXlCsXeedfsbjRHlwJpbbSx9lgIk3PP16sPlYY73hgsvbymgLRdIe+rHK9PTXnJlIGXsHnJ3Ocdh7L82VnAzcopLwIKJ5e8Gc9xaopfIkVRPJCuXpiTS3s+NYTl55+pX2I4mo/FeOF5UNtlabl+rq9b22s/MASFM6PSGz13QQ4EcBUfV6IOPsFLpo3B2yVPUN3c/Qq5q68owyAT0lLVMyAfIHXr3u4PiEZmJ5uxtVjAUFrtgs2FFNwUWvVWOuP32ENYd9Dp/IckWfx6j9eMQXOPPn4g8OboSRGba1CTh+qt2rvIpnC7MafrVb577wk=\n", "extDataMap": {}, "taskId": "ad1b264f88154758b18bb883e3d1bf2e"}, "响应": "{\"code\":1451001,\"message\":\"系统异常\",\"data\":null}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/f610147c9ade437283988ea2c20db574", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"f610147c9ade437283988ea2c20db574\",\"status\":2,\"failReason\":null,\"cert\":[]}}"}
{"url": "http://svs-service.testk8s.tsign.cn/getCsr/request", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"algorithm": "RSA", "dn": {"cn": "甘舰戈", "ou": "411403199408189070", "l": "hz", "st": "zj", "c": "CN"}}, "响应": "{\"success\":true,\"message\":\"执行成功\",\"csr\":\"MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDCPY6/u6g4tPUXHMCZHgAHgFQbxNKYSZUdNMCjY3ZxlbUIxIy+A5Blb4GO5ud98SM2KjFHGPPJ/c8rLYVMbuPluWREjZAhNaoQ47vvyz+CYPHCdZXRY1n/z3Mqt81h41/PkbhS1rq3cXqy8gAlGXh4Eb/EAMZGPLquOpgP8nV91yNMzzxFk+oQ2cSgZSxRMYddFVaPMBiamy6WEwg+DaCj5MKt5MwuXiEK4zcmt/Ia1owAlgOX197pkcJN8CkXAfMfIHEEtsQH1Y+w+pgSXASWvnOc3f+Y1UXphDY9AUP4YxqzCBKP3xHYKTKkll+uTOeXs9Br6omLqb17OdijQ/DNAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHci4Ib56Q8crdlNd+REsgoJDwce8FWC8HgRc8nA9RprNKXeR4igfsxA08PCKSbNxTM87oX2xfrfilg7WcAYk7UFcs2Tky2PV3eTkc3LpUH7sYZrsA1FlNqI5hZDiYoZb0yhfLpdHGeZtL5ety5kr1ZIv6Kxg4hcKQUuy4q/Z6rCWPwbp7rj2lixGa4wIEcMn040MLtN5npA6hV2E20RU2AndSG6RNh8RjFMiPtom3UjU9Fv7GTmwU4/D4mkjOi/G4WGFi586fgD23ouQcEj10efcFckY5JxMFAwZCX7jZqRSccJboYwYfPYVQo+JC4ohz/PnrQyP5hmSS14S5TVK6c=\\n\",\"svsId\":\"3d05ea0e-2ed4-473a-a5c6-82573d5d9a72\"}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/apply-cert", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"csr": "MIICmzCCAYMCAQAwWDESMBAGA1UEAwwJ55SY6Iiw5oiIMRswGQYDVQQLDBI0MTE0MDMxOTk0MDgxODkwNzAxCzAJBgNVBAcMAmh6MQswCQYDVQQIDAJ6ajELMAkGA1UEBhMCQ04wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDCPY6/u6g4tPUXHMCZHgAHgFQbxNKYSZUdNMCjY3ZxlbUIxIy+A5Blb4GO5ud98SM2KjFHGPPJ/c8rLYVMbuPluWREjZAhNaoQ47vvyz+CYPHCdZXRY1n/z3Mqt81h41/PkbhS1rq3cXqy8gAlGXh4Eb/EAMZGPLquOpgP8nV91yNMzzxFk+oQ2cSgZSxRMYddFVaPMBiamy6WEwg+DaCj5MKt5MwuXiEK4zcmt/Ia1owAlgOX197pkcJN8CkXAfMfIHEEtsQH1Y+w+pgSXASWvnOc3f+Y1UXphDY9AUP4YxqzCBKP3xHYKTKkll+uTOeXs9Br6omLqb17OdijQ/DNAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHci4Ib56Q8crdlNd+REsgoJDwce8FWC8HgRc8nA9RprNKXeR4igfsxA08PCKSbNxTM87oX2xfrfilg7WcAYk7UFcs2Tky2PV3eTkc3LpUH7sYZrsA1FlNqI5hZDiYoZb0yhfLpdHGeZtL5ety5kr1ZIv6Kxg4hcKQUuy4q/Z6rCWPwbp7rj2lixGa4wIEcMn040MLtN5npA6hV2E20RU2AndSG6RNh8RjFMiPtom3UjU9Fv7GTmwU4/D4mkjOi/G4WGFi586fgD23ouQcEj10efcFckY5JxMFAwZCX7jZqRSccJboYwYfPYVQo+JC4ohz/PnrQyP5hmSS14S5TVK6c=\n", "extDataMap": {}, "taskId": "f610147c9ade437283988ea2c20db574"}, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"certId\":\"bd0c0fae48e64adfb20106a2a5a9245b\",\"caRoute\":\"GUOXINCA\",\"algorithm\":\"RSA\",\"signCert\":\"MIIENDCCAxygAwIBAgIIXBoAAAAAAOUwDQYJKoZIhvcNAQELBQAwgbMxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnmsZ/oi4/nnIExEjAQBgNVBAcMCeWNl+S6rOW4gjEwMC4GA1UECgwn5rGf6IuP55yB5Zu95L+h5pWw5a2X56eR5oqA5pyJ6ZmQ5YWs5Y+4MTAwLgYDVQQLDCfmsZ/oi4/nnIHlm73kv6HmlbDlrZfnp5HmioDmnInpmZDlhazlj7gxGDAWBgNVBAMMD0dYQ0FfQ2FsaXRlX1JTQTAeFw0yNTA1MjUxNjAwMDBaFw0yNjA1MjYxNTU5NTlaMIGDMQswCQYDVQQGEwJDTjEMMAoGA1UECwwDMDAxMQswCQYDVQQgDAIwMDEMMAoGA1UEWAwDMDAxMRswGQYDVQQBDBI2NDAzODExOTcwMTAxNTQ0MTMxFDASBgNVBC0MC3VzZXJDZXJ0MTAzMRgwFgYDVQQDDA/mtYvor5Xlj7jmpaDnpo8wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDCPY6/u6g4tPUXHMCZHgAHgFQbxNKYSZUdNMCjY3ZxlbUIxIy+A5Blb4GO5ud98SM2KjFHGPPJ/c8rLYVMbuPluWREjZAhNaoQ47vvyz+CYPHCdZXRY1n/z3Mqt81h41/PkbhS1rq3cXqy8gAlGXh4Eb/EAMZGPLquOpgP8nV91yNMzzxFk+oQ2cSgZSxRMYddFVaPMBiamy6WEwg+DaCj5MKt5MwuXiEK4zcmt/Ia1owAlgOX197pkcJN8CkXAfMfIHEEtsQH1Y+w+pgSXASWvnOc3f+Y1UXphDY9AUP4YxqzCBKP3xHYKTKkll+uTOeXs9Br6omLqb17OdijQ/DNAgMBAAGjejB4MA4GA1UdDwEB/wQEAwIAwDARBglghkgBhvhCAQEEBAMCAIAwEwYDVR0lBAwwCgYIKwYBBQUHAwIwHwYDVR0jBBgwFoAU7Zbc/HSeJFnL5s/wfPYB8bJPqREwHQYDVR0OBBYEFHOsYkeyOOodPIbkrWHdXrr2VSAkMA0GCSqGSIb3DQEBCwUAA4IBAQB/sOsTjJqc00eCe69k0tsBxiZM14BU3oHczOrRXywVakW/i8+85q0iKFETg5QulinXgtBioP/VoDY/ekW69G4nLypvqBEE0Yg19mDSXmt+L3FhK4DI2dg6QgNip3i2HpaO5raS9NutgRia7THBSGiXnSsYFBpyL8AhPfyj5uBvcuI3a3mxboimYQtid50a17aA/vO3ksMpB4yRjlR561H5NLwnKHNIoGWvJgeQC2rG7XDNekoaWyLeBkodKuyowZGfk9QhImOvmzj9t9ZCNrU9kr0ouETo1/RAoR0xtEFE5CleU0N2iMaBZ7TFYgouBudkZWE8mWdI1oglqLJ2KFol\",\"encCert\":null,\"signSn\":\"5c1a0000000000e5\",\"startDate\":1748188800000,\"endDate\":1779811199000,\"ext\":null}}"}
{"url": "http://in-sml-openapi.tsign.cn/v1/certs/task/f610147c9ade437283988ea2c20db574", "method": "GET", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": null, "响应": "{\"code\":0,\"message\":\"成功\",\"data\":{\"taskId\":\"f610147c9ade437283988ea2c20db574\",\"status\":3,\"failReason\":null,\"cert\":[{\"certId\":\"bd0c0fae48e64adfb20106a2a5a9245b\",\"caRoute\":\"GUOXINCA\",\"algorithm\":\"RSA\",\"signCert\":\"MIIENDCCAxygAwIBAgIIXBoAAAAAAOUwDQYJKoZIhvcNAQELBQAwgbMxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnmsZ/oi4/nnIExEjAQBgNVBAcMCeWNl+S6rOW4gjEwMC4GA1UECgwn5rGf6IuP55yB5Zu95L+h5pWw5a2X56eR5oqA5pyJ6ZmQ5YWs5Y+4MTAwLgYDVQQLDCfmsZ/oi4/nnIHlm73kv6HmlbDlrZfnp5HmioDmnInpmZDlhazlj7gxGDAWBgNVBAMMD0dYQ0FfQ2FsaXRlX1JTQTAeFw0yNTA1MjUxNjAwMDBaFw0yNjA1MjYxNTU5NTlaMIGDMQswCQYDVQQGEwJDTjEMMAoGA1UECwwDMDAxMQswCQYDVQQgDAIwMDEMMAoGA1UEWAwDMDAxMRswGQYDVQQBDBI2NDAzODExOTcwMTAxNTQ0MTMxFDASBgNVBC0MC3VzZXJDZXJ0MTAzMRgwFgYDVQQDDA/mtYvor5Xlj7jmpaDnpo8wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDCPY6/u6g4tPUXHMCZHgAHgFQbxNKYSZUdNMCjY3ZxlbUIxIy+A5Blb4GO5ud98SM2KjFHGPPJ/c8rLYVMbuPluWREjZAhNaoQ47vvyz+CYPHCdZXRY1n/z3Mqt81h41/PkbhS1rq3cXqy8gAlGXh4Eb/EAMZGPLquOpgP8nV91yNMzzxFk+oQ2cSgZSxRMYddFVaPMBiamy6WEwg+DaCj5MKt5MwuXiEK4zcmt/Ia1owAlgOX197pkcJN8CkXAfMfIHEEtsQH1Y+w+pgSXASWvnOc3f+Y1UXphDY9AUP4YxqzCBKP3xHYKTKkll+uTOeXs9Br6omLqb17OdijQ/DNAgMBAAGjejB4MA4GA1UdDwEB/wQEAwIAwDARBglghkgBhvhCAQEEBAMCAIAwEwYDVR0lBAwwCgYIKwYBBQUHAwIwHwYDVR0jBBgwFoAU7Zbc/HSeJFnL5s/wfPYB8bJPqREwHQYDVR0OBBYEFHOsYkeyOOodPIbkrWHdXrr2VSAkMA0GCSqGSIb3DQEBCwUAA4IBAQB/sOsTjJqc00eCe69k0tsBxiZM14BU3oHczOrRXywVakW/i8+85q0iKFETg5QulinXgtBioP/VoDY/ekW69G4nLypvqBEE0Yg19mDSXmt+L3FhK4DI2dg6QgNip3i2HpaO5raS9NutgRia7THBSGiXnSsYFBpyL8AhPfyj5uBvcuI3a3mxboimYQtid50a17aA/vO3ksMpB4yRjlR561H5NLwnKHNIoGWvJgeQC2rG7XDNekoaWyLeBkodKuyowZGfk9QhImOvmzj9t9ZCNrU9kr0ouETo1/RAoR0xtEFE5CleU0N2iMaBZ7TFYgouBudkZWE8mWdI1oglqLJ2KFol\",\"encCert\":null,\"signSn\":\"5c1a0000000000e5\",\"startDate\":1748188800000,\"endDate\":1779811199000,\"ext\":null}]}}"}
{"url": "http://cert-service.smlk8s.esign.cn/queryCertUser/model", "method": "POST", "headers": {"Content-Type": "application/json", "X-Tsign-Open-App-Id": "**********", "X-Tsign-Open-Auth-Mode": "simple"}, "入参": {"certId": "bd0c0fae48e64adfb20106a2a5a9245b"}, "响应": "{\"success\":true,\"message\":\"执行成功\",\"users\":[]}"}
